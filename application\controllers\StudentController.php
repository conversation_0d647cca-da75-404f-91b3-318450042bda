<?php
defined('BASEPATH') or die('No direct script access allowed!');

use <PERSON>pipu\Html2Pdf\Html2Pdf;
use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * @property MsClass $msclass
 * @property MsStudent $msstudent
 * @property MsTeacher $msteacher
 * @property MsUsers $msusers
 * @property CI_DB_mysqli_driver $db
 * @property Datatables $datatables
 */
class StudentController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsClass', 'msclass');
        $this->load->model('MsStudent', 'msstudent');
        $this->load->model('MsTeacher', 'msteacher');
        $this->load->model('MsUsers', 'msusers');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login/admin'));
        }
        $data = array();
        $data['title'] = 'Manajemen - Peserta Didik';
        $data['content'] = 'admin/master/student/index';

        if (isAdmin()) {
            $data['class'] = $this->msclass->order_by('roman_to_int(a.level),a.name', 'asc')->get(array(
                'a.createdby' => getCurrentIdUser(),
                'a.isdeleted' => null
            ))->result();
        } else if (isTeacher()) {
            $classid = $this->msclass->order_by('roman_to_int(a.level),a.name', 'asc')->get(array(
                'a.teacherid' => getCurrentIdUser(),
                'a.isdeleted' => null
            ));

            if ($classid->num_rows() > 0) {
                $data['class'] = $classid->row()->id;
            } else {
                $data['class'] = null;
            }
        }

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponse();
        }

        $classid = getPost('classid', null);

        $datatable = $this->datatables->make('MsStudent', 'QueryDatatables', 'SearchDatatables');

        $where = array();

        if (isAdmin()) {
            $where['a.createdby'] = getCurrentIdUser();
        } else if (isTeacher()) {
            $where['b.teacherid'] = getCurrentIdUser();
        }

        if ($classid != null) {
            $where['b.id'] = $classid;
        }

        $where["(a.status IS NULL OR a.status = 'Active') ="] = true;
        $where['a.isdeleted'] = null;

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();

            if ($value->picture == null || !file_exists('./uploads/students/' . $value->picture)) {
                if ($value->gender == 'L') {
                    $name = '<div class="d-flex align-items-center">
                    <img src="' . base_url('assets/img/men.png') . '" style="width: 50px; height: 50px; object-fit: cover;" class="rounded-circle me-3">
                    <span class="me-1">' . $value->name . '</span>
                    </div>';
                } else {
                    $name = '<div class="d-flex align-items-center">
                    <img src="' . base_url('assets/img/women.png') . '" style="width: 50px; height: 50px; object-fit: cover;" class="rounded-circle me-3">
                    <span>' . $value->name . '</span>
                    </div>';
                }
            } else {
                $name = '<div class="d-flex align-items-center">
                <img src="' . base_url('uploads/students/' . $value->picture) . '" class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;"">
                <span>' . $value->name . '</span>
                    </div>';
            }

            $actions = "";
            if (isAdmin()) {
                $actions .= "<div class=\"d-flex\">
                    <a href=\"" . base_url('master/student/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2 mb-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                        <i class=\"ti ti-edit\"></i>
                    </a>

                    <button type=\"button\" class=\"btn btn-danger btn-sm ms-2 mb-2\" onclick=\"deleteStudent('" . $value->id . "', '" . $value->name . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                        <i class=\"ti ti-trash\"></i>
                    </button>
                </div>";
            }

            $actionswa = "<a href=\"https://wa.me/" . $value->phonenumber . "\" class=\"btn btn-success btn-sm ms-2\" target=\"_blank\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-success\" data-bs-original-title=\"Whatsapp\">
                <i class=\"ti ti-brand-whatsapp\"></i>
            </a>";

            $actionsrfid = "";
            if (isAdmin()) {

                if ($value->rfidcode != null && $value->rfidcode != '') {
                    $actionsrfid .= " <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"unregisterRfid('" . $value->id . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Unregister RFID\">
                        <i class=\"ti ti-id-off\"></i>
                    </button>";
                } else {
                    $actionsrfid .= " <button type=\"button\" class=\"btn btn-warning btn-sm ms-2\" onclick=\"registerRfid('" . $value->id . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-warning\" data-bs-original-title=\"Register RFID\">
                        <i class=\"ti ti-id\"></i>
                    </button>";
                }
            }

            $actions .= "<div class=\"d-flex\">" . $actionswa . $actionsrfid . "</div>";

            $detail[] = $value->nis;
            $detail[] = $name;

            if ($value->level == null) {
                $detail[] = $value->classname ?? 'UMUM';
            } else {
                $detail[] = $value->level . ' - ' . $value->classname;
            }

            $detail[] = $value->teachername;
            $detail[] = $value->gender == 'L' ? 'Laki-laki' : 'Perempuan';
            $detail[] = $value->parentname;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Tambah Peserta Didik';
        $data['content'] = 'admin/master/student/add';
        $data['class'] = $this->msclass->select('id,name,level')
            ->where(array(
                'createdby' => getCurrentIdUser(),
                'isdeleted' => null
            ))
            ->order_by('roman_to_int(level),name', 'ASC')
            ->get()
            ->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }
        if (!is_dir('./uploads/students/')) {
            mkdir('./uploads/students/', 0777, true);
        }

        $name = getPost('name');
        $nis = getPost('nis');
        $nisn = getPost('nisn');
        $gender = getPost('gender');
        $religion = getPost('religion');
        $classid = getPost('classid');
        $parentname = getPost('parentname');
        $phonenumber = getPost('phonenumber');
        $password = getPost('password', '123456');
        $parent_password = getPost('parent_password', '123456');

        if (empty(strlen(trim($name)))) {
            return JSONResponseDefault('FAILED', 'Nama tidak boleh kosong');
        } else if (empty(strlen(trim($nis)))) {
            return JSONResponseDefault('FAILED', 'NIS tidak boleh kosong');
        } else if (empty(strlen(trim($nisn)))) {
            return JSONResponseDefault('FAILED', 'NISN tidak boleh kosong');
        } else if (empty(strlen(trim($gender)))) {
            return JSONResponseDefault('FAILED', 'Jenis Kelamin tidak boleh kosong');
        } else if ($gender != 'L' && $gender != 'P') {
            return JSONResponseDefault('FAILED', 'Jenis Kelamin tidak valid');
        } else if (empty(strlen(trim($religion)))) {
            return JSONResponseDefault('FAILED', 'Agama tidak boleh kosong');
        } else if (empty(strlen(trim($classid)))) {
            return JSONResponseDefault('FAILED', 'Kelas tidak boleh kosong');
        } else if (empty(strlen(trim($parentname)))) {
            return JSONResponseDefault('FAILED', 'Nama wali murid tidak boleh kosong');
        } else if (empty(strlen(trim($phonenumber)))) {
            return JSONResponseDefault('FAILED', 'No Whatsapp tidak boleh kosong');
        } else if (strlen($phonenumber) < 10) {
            return JSONResponseDefault('FAILED', 'No Whatsapp tidak valid');
        } else if (strlen($phonenumber) > 15) {
            return JSONResponseDefault('FAILED', 'No Whatsapp tidak valid');
        } else if (substr($phonenumber, 0, 2) != '08' && substr($phonenumber, 0, 3) != '628') {
            return JSONResponseDefault('FAILED', 'No Whatsapp tidak valid');
        } else if (substr($phonenumber, 0, 2) == '08') {
            $delete0 = ltrim($phonenumber, '0');
            $phonenumber = '62' . $delete0;
        }

        $ceknis = $this->msstudent->select('nis')->where(array(
            'nis' => $nis,
            'createdby' => getCurrentIdUser(),
            'status' => 'Active',
            'isdeleted' => null
        ))->get();

        if ($ceknis->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'NIS sudah digunakan');
        }

        $ceknisn = $this->msstudent->select('nisn')->where(array(
            'nisn' => $nisn,
            'createdby' => getCurrentIdUser(),
            'isdeleted' => null
        ))->get();

        if ($ceknisn->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'NISN sudah digunakan');
        }

        $config['upload_path'] = './uploads/students/';
        $config['allowed_types'] = 'jpg|jpeg|png';
        $config['max_size'] = 2048;
        $config['encrypt_name'] = true;

        $this->load->library('upload', $config);

        $picture = null;

        if (!empty($_FILES['picture']['name'])) {
            if ($this->upload->do_upload('picture')) {
                $uploadData = $this->upload->data();
                $picture = $uploadData['file_name'];
            } else {
                return JSONResponseDefault('FAILED', $this->upload->display_errors('', ''));
            }
        }

        $insert = array();
        $insert['name'] = strtoupper($name);
        $insert['nis'] = $nis;
        $insert['nisn'] = $nisn;
        $insert['gender'] = $gender;
        $insert['religion'] = $religion;
        $insert['classid'] = $classid;
        $insert['parentname'] = strtoupper($parentname);
        $insert['phonenumber'] = $phonenumber;
        $insert['status'] = 'Active';
        $insert['password'] = password_hash($password, PASSWORD_DEFAULT);
        $insert['parent_password'] = password_hash($parent_password, PASSWORD_DEFAULT);
        $insert['picture'] = $picture ? $picture : '-';
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $insert = $this->msstudent->insert($insert);

        if ($insert) {
            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Data gagal ditambahkan');
        }
    }

    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $cek = $this->msstudent->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser(),
            "(a.status IS NULL OR a.status = 'Active') =" => true,
            'a.isdeleted' => null
        ));

        if ($cek->num_rows() == 0) {
            return redirect(base_url('master/student'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Ubah Peserta Didik';
        $data['content'] = 'admin/master/student/edit';
        $data['class'] = $this->msclass->select('id,name,level')
            ->where(array(
                'createdby' => getCurrentIdUser(),
                'isdeleted' => null
            ))
            ->order_by('roman_to_int(level),name', 'ASC')
            ->get()
            ->result();
        $data['student'] = $cek->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $cek = $this->msstudent->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser(),
            "(a.status IS NULL OR a.status = 'Active') =" => true,
            'a.isdeleted' => null
        ));

        if ($cek->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $cek->row();

        $name = getPost('name');
        $nis = getPost('nis');
        $nisn = getPost('nisn');
        $gender = getPost('gender');
        $religion = getPost('religion');
        $classid = getPost('classid');
        $parentname = getPost('parentname');
        $phonenumber = getPost('phonenumber');
        $password = getPost('password');
        $parent_password = getPost('parent_password');

        if (empty(strlen(trim($name)))) {
            return JSONResponseDefault('FAILED', 'Nama tidak boleh kosong');
        } else if (empty(strlen(trim($nis)))) {
            return JSONResponseDefault('FAILED', 'NIS tidak boleh kosong');
        } else if (empty(strlen(trim($nisn)))) {
            return JSONResponseDefault('FAILED', 'NISN tidak boleh kosong');
        } else if (empty(strlen(trim($gender)))) {
            return JSONResponseDefault('FAILED', 'Jenis Kelamin tidak boleh kosong');
        } else if ($gender != 'L' && $gender != 'P') {
            return JSONResponseDefault('FAILED', 'Jenis Kelamin tidak valid');
        } else if (empty(strlen(trim($religion)))) {
            return JSONResponseDefault('FAILED', 'Agama tidak boleh kosong');
        } else if (empty(strlen(trim($classid)))) {
            return JSONResponseDefault('FAILED', 'Kelas tidak boleh kosong');
        } else if (empty(strlen(trim($parentname)))) {
            return JSONResponseDefault('FAILED', 'Nama wali murid tidak boleh kosong');
        } else if (empty(strlen(trim($phonenumber)))) {
            return JSONResponseDefault('FAILED', 'No Whatsapp tidak boleh kosong');
        } else if (strlen($phonenumber) < 10) {
            return JSONResponseDefault('FAILED', 'No Whatsapp tidak valid');
        } else if (strlen($phonenumber) > 15) {
            return JSONResponseDefault('FAILED', 'No Whatsapp tidak valid');
        } else if (substr($phonenumber, 0, 2) != '08' && substr($phonenumber, 0, 3) != '628') {
            return JSONResponseDefault('FAILED', 'No Whatsapp tidak valid');
        } else if (substr($phonenumber, 0, 2) == '08') {
            $delete0 = ltrim($phonenumber, '0');
            $phonenumber = '62' . $delete0;
        }

        if ($nis != $row->nis) {
            $ceknis = $this->msstudent->select('nis')->where(array(
                'nis' => $nis,
                'createdby' => getCurrentIdUser(),
                'status' => 'Active',
                'id !=' => $id,
                'isdeleted' => null
            ))->get();

            if ($ceknis->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'NIS sudah digunakan');
            }
        }

        if ($nisn != $row->nisn) {
            $ceknisn = $this->msstudent->select('nisn')->where(array(
                'nisn' => $nisn,
                'createdby' => getCurrentIdUser(),
                'isdeleted' => null,
                'id !=' => $id
            ))->get();

            if ($ceknisn->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'NISN sudah digunakan');
            }
        }

        $config['upload_path'] = './uploads/students/';
        $config['allowed_types'] = 'jpg|jpeg|png';
        $config['max_size'] = 2048; // 2MB
        $config['encrypt_name'] = true;

        $this->load->library('upload', $config);

        $picture = $row->picture;

        if (!empty($_FILES['picture']['name'])) {
            if ($this->upload->do_upload('picture')) {
                $uploadData = $this->upload->data();

                if (!empty($picture) && file_exists('./uploads/students/' . $picture)) {
                    unlink('./uploads/students/' . $picture);
                }

                $picture = $uploadData['file_name'];
            } else {
                return JSONResponseDefault('FAILED', $this->upload->display_errors('', ''));
            }
        }


        $update = array();
        $update['name'] = strtoupper($name);
        $update['nis'] = $nis;
        $update['nisn'] = $nisn;
        $update['gender'] = $gender;
        $update['religion'] = $religion;
        $update['classid'] = $classid;
        $update['parentname'] = strtoupper($parentname);
        $update['phonenumber'] = $phonenumber;
        $update['picture'] = $picture;

        if (!empty($password)) {
            $update['password'] = password_hash($password, PASSWORD_DEFAULT);
        }

        if (!empty($parent_password)) {
            $update['parent_password'] = password_hash($parent_password, PASSWORD_DEFAULT);
        }

        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $update = $this->msstudent->update(array('id' => $id), $update);

        if ($update) {
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Data gagal diubah');
        }
    }

    public function process_delete()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->msstudent->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser(),
            "(a.status IS NULL OR a.status = 'Active') =" => true,
            'isdeleted' => null
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $delete = $this->msstudent->update(array('id' => $id), array(
            'isdeleted' => 1,
        ));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Data gagal dihapus');
        }
    }

    public function format_excel()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login/admin'));
        }

        return force_download('./assets/format_excel/FORMAT IMPORT EXCEL PESERTA DIDIK.xlsx', null);
    }

    public function import_excel()
    {
        if (!isLogin() || !isAdmin()) {
            return;
        } else {
            echo $this->load->view('admin/master/student/import', array(), true);
        }
    }

    public function process_import_excel()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isLogin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            if (isset($_FILES["file"]["name"])) {
                $file_tmp = $_FILES['file']['tmp_name'];

                // Gunakan Reader Mode untuk menghemat memori
                $reader = IOFactory::createReader('Xlsx');
                $reader->setReadDataOnly(true);
                $spreadsheet = $reader->load($file_tmp);
                $worksheet = $spreadsheet->getActiveSheet();

                $highestRow = $worksheet->getHighestRow();
                $batchInsert = [];
                $batchUpdate = [];
                $classCache = [];

                $successUpdate = 0;
                $successAdd = 0;
                $dataValid = 0;

                for ($row = 2; $row <= $highestRow; $row++) {
                    $nisn = trim($worksheet->getCellByColumnAndRow(1, $row)->getValue() ?? '');
                    $nis = trim($worksheet->getCellByColumnAndRow(2, $row)->getValue() ?? '');
                    $name = trim($worksheet->getCellByColumnAndRow(3, $row)->getValue() ?? '');
                    $gender = strtoupper(trim($worksheet->getCellByColumnAndRow(4, $row)->getValue() ?? ''));
                    $parentname = trim($worksheet->getCellByColumnAndRow(5, $row)->getValue() ?? '');
                    $phonenumber = trim($worksheet->getCellByColumnAndRow(6, $row)->getValue() ?? '');
                    $levelclass = trim($worksheet->getCellByColumnAndRow(7, $row)->getValue() ?? '');
                    $classname = trim($worksheet->getCellByColumnAndRow(8, $row)->getValue() ?? '');
                    $religion = trim($worksheet->getCellByColumnAndRow(9, $row)->getValue() ?? '');
                    $rfidcode = trim($worksheet->getCellByColumnAndRow(10, $row)->getValue() ?? '');

                    if (empty($nisn) || empty($nis) || empty($name) || empty($gender) || empty($levelclass) || empty($classname)) {
                        continue;
                    }

                    if (!in_array($gender, ['L', 'P'])) {
                        $gender = ($gender == 'LAKI-LAKI') ? 'L' : (($gender == 'PEREMPUAN') ? 'P' : null);
                        if (!$gender) continue;
                    }

                    if (!is_numeric($nis) || ($phonenumber && !is_numeric($phonenumber))) {
                        continue;
                    } else {
                        if ($phonenumber) {
                            if (strlen($phonenumber) < 10) {
                                $phonenumber = null;
                            } else if (strlen($phonenumber) > 15) {
                                $phonenumber = null;
                            } else if (substr($phonenumber, 0, 2) != '08' && substr($phonenumber, 0, 3) != '628') {
                                $phonenumber = null;
                            } else if (substr($phonenumber, 0, 2) == '08') {
                                $delete0 = ltrim($phonenumber, '0');
                                $phonenumber = '62' . $delete0;
                            }
                        }
                    }

                    if ($religion) {
                        $religion = strtoupper($religion);
                        if ($religion == 'ISLAM') {
                            $religion = 'Islam';
                        } else if ($religion == 'KRISTEN') {
                            $religion = 'Kristen';
                        } else if ($religion == 'KATOLIK') {
                            $religion = 'Katolik';
                        } else if ($religion == 'HINDU') {
                            $religion = 'Hindu';
                        } else if ($religion == 'BUDHA') {
                            $religion = 'Budha';
                        } else if ($religion == 'KONGHUCU') {
                            $religion = 'Konghucu';
                        } else {
                            $religion = null;
                        }
                    }

                    // Cache untuk mengurangi query kelas berulang
                    $classKey = strtolower($classname) . '_' . $levelclass;
                    if (!isset($classCache[$classKey])) {
                        $getClass = $this->msclass->get([
                            'LOWER(a.name) =' => strtolower($classname),
                            'createdby' => getCurrentIdUser(),
                            'level' => $levelclass,
                            'isdeleted' => null,
                        ]);

                        if ($getClass->num_rows() == 0) {
                            $insertClass = [
                                'level' => strtoupper(trim($levelclass)),
                                'name' => strtoupper(trim($classname)),
                                'createddate' => getCurrentDate(),
                                'createdby' => getCurrentIdUser(),
                            ];
                            $this->msclass->insert($insertClass);
                            $classCache[$classKey] = $this->db->insert_id();
                        } else {
                            $classCache[$classKey] = $getClass->row()->id;
                        }
                    }

                    $classId = $classCache[$classKey];

                    // Simpan data dalam batch
                    $existingStudent = $this->msstudent->get([
                        'nisn' => $nisn,
                        'createdby' => getCurrentIdUser(),
                        'isdeleted' => null,
                    ]);
                    if ($existingStudent->num_rows() > 0) {

                        // NIS
                        $cekNis = $this->msstudent->get([
                            'nis' => $nis,
                            'createdby' => getCurrentIdUser(),
                            'status' => 'Active',
                            'isdeleted' => null,
                            'id !=' => $existingStudent->row()->id
                        ]);

                        if ($cekNis->num_rows() > 0) {
                            continue;
                        }

                        //rfidcode

                        if (!empty($rfidcode) && $rfidcode != $existingStudent->row()->rfidcode) {
                            $cekrfidteacher = $this->msteacher->get(array(
                                'rfidcode' => $rfidcode,
                                'isdeleted' => null,
                                'createdby' => getCurrentIdUser(),
                            ));

                            if ($cekrfidteacher->num_rows() > 0) {
                                $rfidcode = null;
                            } else {
                                $cekrfidstudent = $this->msstudent->get(array(
                                    'rfidcode' => $rfidcode,
                                    'createdby' => getCurrentIdUser(),
                                    'status' => 'Active',
                                    'isdeleted' => null,
                                    'id !=' => $existingStudent->row()->id
                                ));

                                if ($cekrfidstudent->num_rows() > 0) {
                                    $rfidcode = null;
                                }
                            }
                        }

                        // Update
                        $batchUpdate[] = [
                            'id' => $existingStudent->row()->id,
                            'name' => $name,
                            'gender' => $gender,
                            'parentname' => $parentname,
                            'phonenumber' => $phonenumber,
                            'classid' => $classId,
                            'rfidcode' => $rfidcode,
                            'religion' => $religion,
                        ];
                        $successUpdate++;
                    } else {

                        // NIS

                        $cekNis = $this->msstudent->get([
                            'nis' => $nis,
                            'createdby' => getCurrentIdUser(),
                            'status' => 'Active',
                            'isdeleted' => null
                        ]);

                        if ($cekNis->num_rows() > 0) {
                            continue;
                        }

                        //rfidcode

                        if (!empty($rfidcode)) {
                            $cekrfidteacher = $this->msteacher->get(array(
                                'rfidcode' => $rfidcode,
                                'isdeleted' => null,
                                'createdby' => getCurrentIdUser(),
                            ));

                            if ($cekrfidteacher->num_rows() > 0) {
                                $rfidcode = null;
                            } else {
                                $cekrfidstudent = $this->msstudent->get(array(
                                    'rfidcode' => $rfidcode,
                                    'createdby' => getCurrentIdUser(),
                                    'status' => 'Active',
                                    'isdeleted' => null
                                ));

                                if ($cekrfidstudent->num_rows() > 0) {
                                    $rfidcode = null;
                                }
                            }
                        }

                        // Insert
                        $batchInsert[] = [
                            'nisn' => $nisn,
                            'nis' => $nis,
                            'name' => $name,
                            'gender' => $gender,
                            'parentname' => $parentname,
                            'phonenumber' => $phonenumber,
                            'classid' => $classId,
                            'rfidcode' => $rfidcode,
                            'religion' => $religion,
                            'status' => 'Active',
                            'password' => password_hash('123456', PASSWORD_DEFAULT),
                            'createddate' => getCurrentDate(),
                            'createdby' => getCurrentIdUser(),
                        ];
                        $successAdd++;
                    }

                    $dataValid++;

                    // Proses batch setiap 500 data untuk efisiensi
                    if (count($batchInsert) >= 500) {
                        $this->msstudent->insert_batch($batchInsert);
                        $batchInsert = []; // Reset batch
                    }

                    if (count($batchUpdate) >= 500) {
                        $this->msstudent->update_batch($batchUpdate, 'id');
                        $batchUpdate = []; // Reset batch
                    }
                }

                // Proses data yang tersisa
                if (!empty($batchInsert)) {
                    $this->msstudent->insert_batch($batchInsert);
                }

                if (!empty($batchUpdate)) {
                    $this->msstudent->update_batch($batchUpdate, 'id');
                }
            }

            if ($this->db->trans_status() === false) {
                throw new Exception('Gagal memproses data.');
            }

            $this->db->trans_commit();
            return JSONResponseDefault(
                'OK',
                "Data berhasil diimport. Data valid: $dataValid. Data ditambahkan: $successAdd. Data diperbarui: $successUpdate."
            );
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function export_excel()
    {
        if (!isLogin() || (!isAdmin() && !isTeacher())) {
            return redirect(base_url('auth/login/admin'));
        }

        $classid = getGet('classid', null);

        $where = ["(a.status IS NULL OR a.status = 'Active') =" => true];
        $where['a.isdeleted'] = null;

        if ($classid) {
            $where['a.classid'] = $classid;
        }

        if (isAdmin()) {
            $where['a.createdby'] = getCurrentIdUser();
        } elseif (isTeacher()) {
            $where['b.teacherid'] = getCurrentIdUser();
        }

        // Create spreadsheet and sheet
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Define common style
        $style = [
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'top' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN],
                'right' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN],
                'bottom' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN],
                'left' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN],
            ]
        ];

        $style_col = $style; // Apply common style for header

        // Header cell style
        $style_row = [
            'alignment' => ['vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER],
            'borders' => $style['borders']
        ];

        // Set title
        $sheet->setCellValue('A1', "DATA PESERTA DIDIK");
        $sheet->mergeCells('A1:H1');
        $sheet->getStyle('A1:H1')->applyFromArray($style);

        // Header Row
        $headers = ["No", "NIS", "NAMA", "JENIS KELAMIN", "KELAS", "WALI KELAS", "WALI MURID", "NOMOR HANDPHONE WALI MURID"];
        foreach (range('A', 'H') as $index => $col) {
            $sheet->setCellValue($col . '3', $headers[$index]);
            $sheet->getStyle($col . '3')->applyFromArray($style_col);
        }

        // Fetch student data
        $student = $this->msstudent->select('a.*,b.name as classname,b.level,c.name as teachername')
            ->join('msclass b', 'b.id = a.classid AND b.isdeleted IS NULL', 'LEFT')
            ->join('msteacher c', 'c.id = b.teacherid AND c.status = "Active" AND c.isdeleted IS NULL', 'LEFT')
            ->order_by('b.name,a.name', 'ASC')
            ->result($where);

        $numrow = 4; // Starting row for data
        foreach ($student as $key => $data) {
            $sheet->setCellValue('A' . $numrow, $key + 1);
            $sheet->setCellValueExplicit('B' . $numrow, $data->nis, \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);
            $sheet->setCellValue('C' . $numrow, $data->name);
            $sheet->setCellValue('D' . $numrow, $data->gender == 'L' ? 'Laki-laki' : 'Perempuan');
            $sheet->setCellValueExplicit('E' . $numrow, ($data->level ? $data->level . ' - ' : '') . $data->classname, \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);
            $sheet->setCellValue('F' . $numrow, $data->teachername);
            $sheet->setCellValue('G' . $numrow, $data->parentname);
            $sheet->setCellValueExplicit('H' . $numrow, $data->phonenumber, \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);

            // Apply row style
            foreach (range('A', 'H') as $col) {
                $sheet->getStyle($col . $numrow)->applyFromArray($style_row);
            }

            $numrow++;
        }

        // Set column widths
        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(30);
        $sheet->getColumnDimension('C')->setWidth(50);
        $sheet->getColumnDimension('D')->setWidth(20);
        $sheet->getColumnDimension('E')->setWidth(20);
        $sheet->getColumnDimension('F')->setWidth(40);
        $sheet->getColumnDimension('G')->setWidth(40);
        $sheet->getColumnDimension('H')->setWidth(40);

        // Auto row height
        $sheet->getDefaultRowDimension()->setRowHeight(-1);

        // Set page orientation
        $sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);

        // Set title for the Excel sheet
        $sheet->setTitle("Laporan Data Peserta Didik");

        // Export as Excel file
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="Laporan Data Peserta Didik.xlsx"');
        header('Cache-Control: max-age=0');

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');
    }

    public function export_pdf()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $classid = getGet('classid', null);

        $where = array();

        if ($classid) {
            $where['b.id'] = $classid;
        }

        if (isAdmin()) {
            $where['a.createdby'] = getCurrentIdUser();
        } else if (isTeacher()) {
            $where['b.teacherid'] = getCurrentIdUser();
        }

        $where["(a.status IS NULL OR a.status = 'Active') ="] = true;
        $where['a.isdeleted'] = null;

        $student =  $this->msstudent->select('a.*,b.level,b.name as classname, c.name as teachername')
            ->join('msclass b', 'a.classid = b.id AND b.isdeleted IS NULL', 'LEFT')
            ->join('msteacher c', 'b.teacherid = c.id AND c.status = "Active" AND c.isdeleted IS NULL', 'LEFT')
            ->where($where)
            ->order_by('b.name,a.name', 'ASC')
            ->result();

        $school = $this->msusers->get(array('id' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()))->row();

        $html2pdf = new Html2Pdf('L', 'A4', 'en', true, 'UTF-8', array(5, 5, 5, 5));
        $html2pdf->writeHTML($this->load->view('admin/master/student/pdf', array(
            'student' => $student,
            'school' => $school
        ), true));

        $html2pdf->output();
    }

    public function registercard()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $getstudent = $this->msstudent->select('a.*,b.name as classname,b.level,c.name as teachername')
            ->join('msclass b', 'a.classid = b.id AND b.isdeleted IS NULL', 'LEFT')
            ->join('msteacher c', 'b.teacherid = c.id AND c.status = "Active" AND c.isdeleted IS NULL', 'LEFT')
            ->where(array(
                "(a.rfidcode IS NULL OR a.rfidcode = '') =" => TRUE,
                'a.createdby' => getCurrentIdUser(),
                '(a.status IS NULL OR a.status = "Active") =' => TRUE,
                'a.isdeleted' => null
            ))
            ->order_by('roman_to_int(b.level),b.name,a.name', 'ASC')
            ->get()->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('admin/master/student/registercard/index', array('student' => $getstudent), true)
        ));
    }

    public function process_registercard()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('studentid');
        $rfidcode = getPost('code');

        if (empty(strlen(trim($rfidcode)))) {
            return JSONResponseDefault('FAILED', 'RFID Code tidak boleh kosong');
        }

        $cek = $this->msstudent->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser(),
            "(a.status IS NULL OR a.status = 'Active') =" => TRUE,
            'isdeleted' => null
        ));

        if ($cek->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data peserta didik tidak ditemukan');
        }

        $row = $cek->row();

        if ($row->rfidcode != null && $row->rfidcode != '') {
            return JSONResponseDefault('FAILED', 'Peserta didik yang anda masukkan telah terdaftar di Kartu RFID');
        }

        $get = $this->msstudent->get(array(
            'rfidcode' => $rfidcode,
            'createdby' => getCurrentIdUser(),
            "(a.status IS NULL OR a.status = 'Active') =" => TRUE,
            'isdeleted' => null
        ));

        if ($get->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'RFID yang anda masukkan telah terdaftar oleh peserta didik lain');
        }

        $cekrfidteacher = $this->msteacher->get(array(
            'rfidcode' => $rfidcode,
            'isdeleted' => null,
            'createdby' => getCurrentIdUser()
        ));

        if ($cekrfidteacher->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'RFID yang anda masukkan telah terdaftar oleh guru');
        }

        $update = array();
        $update['rfidcode'] = $rfidcode;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $update = $this->msstudent->update(array('id' => $id), $update);

        if ($update) {
            $getnextstudent = $this->msstudent->select('a.*,b.name as classname,b.level,c.name as teachername')
                ->join('msclass b', 'a.classid = b.id AND b.isdeleted IS NULL', 'LEFT')
                ->join('msteacher c', 'b.teacherid = c.id AND c.status = "Active" AND c.isdeleted IS NULL', 'LEFT')
                ->where(array(
                    "(a.rfidcode IS NULL OR a.rfidcode = '') =" => TRUE,
                    'a.createdby' => getCurrentIdUser(),
                    '(a.status IS NULL OR a.status = "Active") =' => TRUE,
                    'a.isdeleted' => null
                ))
                ->order_by('roman_to_int(b.level),b.name,a.name', 'ASC')
                ->get();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'MESSAGE' => 'Peserta didik ' . $row->name . ' berhasil terdaftar di Kartu RFID',
                'NEXTSTUDENT' => $getnextstudent->num_rows(),
                'STUDENT' => $getnextstudent->row()
            ));
        } else {
            return JSONResponseDefault('FAILED', 'Peserta didik gagal terdaftar di Kartu RFID');
        }
    }


    public function history_registercard()
    {
        if (!isLogin() || !isAdmin()) {
            return;
        }

        $getstudent = $this->msstudent->select('a.*,b.name as classname,b.level,c.name as teachername')
            ->join('msclass b', 'a.classid = b.id AND b.isdeleted IS NULL', 'LEFT')
            ->join('msteacher c', 'b.teacherid = c.id AND c.status = "Active" AND c.isdeleted IS NULL', 'LEFT')
            ->where(array(
                "(a.rfidcode IS NOT NULL OR a.rfidcode = '') =" => TRUE,
                'a.createdby' => getCurrentIdUser(),
                '(a.status IS NULL OR a.status = "Active") =' => TRUE,
                'a.isdeleted' => null
            ))
            ->get()->result();

        echo $this->load->view('admin/master/student/registercard/history', array('history' => $getstudent), true);
    }

    public function registerrfid()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $getstudent = $this->msstudent->select('a.*,b.name as classname,b.level')
            ->join('msclass b', 'a.classid = b.id AND b.isdeleted IS NULL', 'LEFT')
            ->where(array(
                'a.id' => $id,
                'a.createdby' => getCurrentIdUser(),
                '(a.status IS NULL OR a.status = "Active") =' => TRUE,
                'a.isdeleted' => null
            ))
            ->get()->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('admin/master/student/registercard/student', array('student' => $getstudent), true)
        ));
    }

    public function process_registerrfid()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('studentid');
        $code = getPost('code');

        $cek = $this->msstudent->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser(),
            "(a.status IS NULL OR a.status = 'Active') =" => TRUE,
            'isdeleted' => null
        ));

        if ($cek->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data peserta didik tidak ditemukan');
        }

        $row = $cek->row();

        if ($row->rfidcode != null && $row->rfidcode != '') {
            return JSONResponseDefault('FAILED', 'Peserta didik yang anda masukkan telah terdaftar di Kartu RFID');
        }

        $get = $this->msstudent->get(array(
            'rfidcode' => $code,
            'createdby' => getCurrentIdUser(),
            "(a.status IS NULL OR a.status = 'Active') =" => TRUE,
            'isdeleted' => null
        ));

        if ($get->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'RFID yang anda masukkan telah terdaftar oleh peserta didik lain');
        }

        $cekrfidteacher = $this->msteacher->get(array(
            'rfidcode' => $code,
            'isdeleted' => null,
            'createdby' => getCurrentIdUser()
        ));

        if ($cekrfidteacher->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'RFID yang anda masukkan telah terdaftar oleh guru');
        }

        $update = array();
        $update['rfidcode'] = $code;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $update = $this->msstudent->update(array('id' => $id), $update);

        if ($update) {
            return JSONResponseDefault('OK', 'Data peserta didik berhasil terdaftar di Kartu RFID');
        } else {
            return JSONResponseDefault('FAILED', 'Data peserta didik gagal terdaftar di Kartu RFID');
        }
    }

    public function unregisterrfid()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $cek = $this->msstudent->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser(),
            "(a.status IS NULL OR a.status = 'Active') =" => TRUE,
            'isdeleted' => null
        ));

        if ($cek->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data peserta didik tidak ditemukan');
        }

        $row = $cek->row();

        if ($row->rfidcode == null || $row->rfidcode == '') {
            return JSONResponseDefault('FAILED', 'Peserta didik yang anda masukkan tidak terdaftar di Kartu RFID');
        }

        $update = array();
        $update['rfidcode'] = null;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $update = $this->msstudent->update(array('id' => $id), $update);

        if ($update) {
            return JSONResponseDefault('OK', 'Data peserta didik berhasil diunregister dari Kartu RFID');
        } else {
            return JSONResponseDefault('FAILED', 'Data peserta didik gagal diunregister dari Kartu RFID');
        }
    }
}
