<?php

use PhpOffice\PhpSpreadsheet\Writer\Ods\Thumbnails;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsNews $msnews
 * @property MsCategoryNews $mscategorynews
 * @property CI_DB_query_builder $db
 * @property Datatables $datatables
 */
class NewsController extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->model('MsCategoryNews', 'mscategorynews');
        $this->load->model('MsNews', 'msnews');
    }

    public function index()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Berita Sekolah - Berita';
        $data['content'] = 'admin/master/news/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('MsNews', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.createdby'] = getCurrentIdUser();

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $detail[] = '<img src="' . base_url('uploads/news/' . $value->thumbnail) . '" style="width: 150px; height: 150px; object-fit: cover; object-position: center;">';
            $detail[] = $value->title;
            $detail[] = $value->category_name;
            $detail[] = $value->content;

            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('master/news/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"deleteNews('" . $value->id .  "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                    <i class=\"ti ti-trash\"></i>
                </button>
            </div>";

            $detail[] = $actions;
            $data[] = $detail;
        }
        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['kategori'] = $this->mscategorynews->get(array('createdby' => getCurrentIdUser()), null, null, 'name ASC')->result();
        $data['title'] = 'Berita Sekolah - Tambah Berita';
        $data['content'] = 'admin/master/news/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        // Ambil data dari input
        $title = trim(getPost('title'));
        $categoryid = getPost('kategori_id');
        $content = getPost('isi') ? trim(getPost('isi')) : '';
        $thumbnail = isset($_FILES['thumbnail']) ? $_FILES['thumbnail'] : null;

        // Validasi input
        if (empty($title)) {
            return JSONResponseDefault('FAILED', 'Judul Berita tidak boleh kosong');
        }

        if (empty($categoryid)) {
            return JSONResponseDefault('FAILED', 'Kategori Berita tidak boleh kosong');
        }

        if (empty($content) || $content === '<p><br></p>') {
            return JSONResponseDefault('FAILED', 'Isi Berita tidak boleh kosong');
        }

        $kategori_exists = $this->mscategorynews->get(['id' => $categoryid])->num_rows();

        if ($kategori_exists == 0) {
            return JSONResponseDefault('FAILED', 'Kategori Berita tidak valid');
        }

        $thumbnail_path = null;
        if (!empty($thumbnail) && $thumbnail['size'] > 0) {
            $config['upload_path'] = './uploads/news/';
            $config['allowed_types'] = 'jpg|jpeg|png|webp';
            $config['max_size'] = 3048; // Maksimum 3MB
            $config['encrypt_name'] = true;

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('thumbnail')) {
                return JSONResponseDefault('FAILED', 'Pastikan ukuran file tidak lebih dari 3 MB');
            }
            $upload_data = $this->upload->data();
            $thumbnail_path = $upload_data['file_name'];
        }

        $insert = [
            'title' => $title,
            'categoryid' => $categoryid,
            'content' => $content,
            'thumbnail' => $thumbnail_path,
            'createddate' => getCurrentDate(),
            'createdby' => getCurrentIdUser(),
        ];

        $insert_result = $this->msnews->insert($insert);

        if ($insert_result) {
            return JSONResponseDefault('OK', 'Data Berita berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Data Berita gagal ditambahkan');
        }
    }

    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $news  = $this->msnews->get(array('id' => $id));

        if ($news->num_rows() == 0) {
            return redirect('master/news');
        }

        $data = array();
        $data['kategori'] = $this->mscategorynews->get(array('createdby' => getCurrentIdUser()), null, null, 'name ASC')->result();
        $data['news'] = $news->row();
        $data['title'] = 'Berita Sekolah - Ubah Berita';
        $data['content'] = 'admin/master/news/edit';

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $news = $this->msnews->get(array('id' => $id, 'createdby' => getCurrentIdUser()));

        if ($news->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $title = trim(getPost('title'));
        $categoryid = getPost('kategori_id');
        $content = getPost('isi');
        $thumbnail = $_FILES['thumbnail'];

        if (empty($title)) {
            return JSONResponseDefault('FAILED', 'Judul Berita Tidak boleh kosong');
        } else if (empty($categoryid)) {
            return JSONResponseDefault('FAILED', 'Kategori Berita Tidak boleh kosong');
        } else if (empty($content)) {
            return JSONResponseDefault('FAILED', 'Isi Berita Tidak boleh kosong');
        }

        $kategori_exists = $this->mscategorynews->get(array('id' => $categoryid))->num_rows();
        if ($kategori_exists == 0) {
            return JSONResponseDefault('FAILED', 'Kategori tidak valid!');
        }

        $update = array();
        $update['title'] = $title;
        $update['categoryid'] = $categoryid;
        $update['content'] = $content;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $old_thumbnail = $news->row()->thumbnail;

        if ($thumbnail['size'] > 0) {
            $config['upload_path'] = './uploads/news/';
            $config['allowed_types'] = 'jpg|jpeg|png|webp';
            $config['max_size'] = 3048;
            $config['encrypt_name'] = true;

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('thumbnail')) {
                return JSONResponseDefault('FAILED', 'Pastikan ukuran file tidak lebih dari 3 MB');
            }

            $upload_data = $this->upload->data();
            $thumbnail_path = './uploads/news/' . $upload_data['file_name'];
            $update['thumbnail'] = $upload_data['file_name'];

            if (!empty($old_thumbnail) && file_exists('./uploads/news/' . $old_thumbnail)) {
                unlink('./uploads/news/' . $old_thumbnail);
            }
        }

        $update_result = $this->msnews->update(array('id' => $id), $update);

        if ($update_result) {
            return JSONResponseDefault('OK', 'Data Berita berhasil diperbarui');
        } else {
            return JSONResponseDefault('FAILED', 'Data Berita gagal diperbarui');
        }
    }


    public function process_delete()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $id = getPost('id');

        if (empty($id)) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $news = $this->msnews->get(array('id' => $id));

        if ($news->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $thumbnail = $news->row()->thumbnail;

        if (!empty($thumbnail)) {
            $filePath = './uploads/news/' . $thumbnail;

            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }

        $delete = $this->msnews->delete(array('id' => $id));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data Berita berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Data Berita gagal dihapus');
        }
    }
}
