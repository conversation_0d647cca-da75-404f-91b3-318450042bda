<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsClass $msclass
 * @property MsStudent $msstudent
 * @property MsAnnouncement $msannouncement
 * @property MsAnnouncementdetail $msannouncementdetail
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 * @property Datatables $datatables
 */
class AnnouncementController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsClass', 'msclass');
        $this->load->model('MsStudent', 'msstudent');
        $this->load->model('MsAnnouncement', 'msannouncement');
        $this->load->model('MsAnnouncementdetail', 'msannouncementdetail');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Pengumuman';
        $data['content'] = 'admin/master/announcement/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('MsAnnouncement', 'QueryDatatables', 'SearchDatatables');

        $where = array(
            'a.academicyeardetailid' => getSessionValue('ACADEMICYEARDETAILID'),
        );

        if (isAdmin()) {
            $where['a.createdby'] = getCurrentIdUser();
        } else if (isTeacher()) {
            $where['a.createdby'] = getCurrentIdSchool();
        } else if (isStudent()) {
            $classid = getCurrentStudent()->classid;
            $where['a.createdby'] = getCurrentIdSchool();
            $where['c.id'] = $classid;
        }

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();

            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('master/announcement/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2 mb-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm ms-2 mb-2\" onclick=\"deleteAnnouncement('" . $value->id . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                    <i class=\"ti ti-trash\"></i>
                </button>
            </div>";

            $detail[] = tgl_indo(DateFormat($value->createddate, 'Y-m-d'));
            $detail[] = "<ul>" . $value->classname . "</ul>";
            $detail[] = $value->description;

            if (isAdmin()) {
                $detail[] = $actions;
            }

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Tambah Pengumuman';
        $data['content'] = 'admin/master/announcement/add';
        $data['class'] = $this->msclass->order_by('roman_to_int(a.level),a.name', 'asc')->get(array(
            'a.createdby' => getCurrentIdUser(),
            'a.isdeleted' => null
        ))->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {

        try {

            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $classid = getPost('classid', array());
            $description = getPost('description', '');

            if (count($classid) == 0) {
                throw new Exception('Kelas tidak boleh kosong');
            } else if (empty(trim($description))) {
                throw new Exception('Keterangan tidak boleh kosong');
            }

            $insert = array(
                'description' => $description,
                'createddate' => getCurrentDate(),
                'createdby' => getCurrentIdUser(),
                'academicyeardetailid' => getSessionValue('ACADEMICYEARDETAILID'),
            );

            $this->msannouncement->insert($insert);
            $lastid = $this->db->insert_id();

            foreach ($classid as $key => $value) {
                $insertdetail = array(
                    'announcementid' => $lastid,
                    'classid' => $value,
                    'createddate' => getCurrentDate(),
                    'createdby' => getCurrentIdUser()
                );

                $this->msannouncementdetail->insert($insertdetail);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal ditambahkan');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $cek = $this->msannouncement->get(array(
            'id' => $id
        ));

        if ($cek->num_rows() == 0) {
            return redirect(base_url('master/announcement'));
        }

        $data = array();
        $data['title'] = 'Ubah Pengumuman';
        $data['content'] = 'admin/master/announcement/edit';
        $data['class'] = $this->msclass->order_by('roman_to_int(a.level),a.name', 'asc')->get(array(
            'a.createdby' => getCurrentIdUser(),
            'a.isdeleted' => null
        ))->result();
        $data['selectedclass'] = $this->msannouncementdetail->get(array('announcementid' => $id))->result();
        $data['announcement'] = $cek->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $cek = $this->msannouncement->get(array(
                'id' => $id
            ));

            if ($cek->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $classid = getPost('classid', array());
            $description = getPost('description', '');

            if (count($classid) == 0) {
                throw new Exception('Kelas tidak boleh kosong');
            } else if (empty(trim($description))) {
                throw new Exception('Keterangan tidak boleh kosong');
            }

            $update = array(
                'description' => $description,
                'updateddate' => getCurrentDate(),
                'updatedby' => getCurrentIdUser(),
            );
            $this->msannouncement->update(array('id' => $id), $update);

            $getclass = $this->msannouncementdetail->get(array(
                'announcementid' => $id
            ))->result();

            foreach ($classid as $value) {
                if (!in_array($value, array_column($getclass, 'classid'))) {
                    $this->msannouncementdetail->insert(array(
                        'announcementid' => $id,
                        'classid' => $value,
                        'createddate' => getCurrentDate(),
                        'createdby' => getCurrentIdUser()
                    ));
                }
            }

            $this->msannouncementdetail->where('announcementid', $id)
                ->where_not_in('classid', $classid)
                ->delete();

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal diubah');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil diubah');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_delete()
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $id = getPost('id');

            $get = $this->msannouncement->get(array(
                'id' => $id
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $this->msannouncement->delete(array('id' => $id));
            $this->msannouncementdetail->delete(array('announcementid' => $id));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal dihapus');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil dihapus');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }
}
