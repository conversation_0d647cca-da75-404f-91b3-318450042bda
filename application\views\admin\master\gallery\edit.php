<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<h4 class="py-3 mb-4">
    <span class="text-muted fw-light">Data Sekolah / Galeri / </span> Ubah Galeri
</h4>

<div class="row">
    <div class="col-md-12">
        <form id="frmGallery" action="<?= base_url('master/gallery/edit/' . $galeri->id . '/process') ?>" method="POST"
            enctype="multipart/form-data">
            <div class="card mb-3">
                <h5 class="card-header">Formulir Ubah Galeri</h5>

                <div class="card-body">
                    <!-- Wrapper untuk kategori dan input gambar -->
                    <div class="row align-items-start">
                        <!-- Dropdown Kategori -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="kategori_id" class="form-label">Kate<PERSON><PERSON> Galeri <span class="text-danger">*</span></label>
                                <select name="kategori_id" id="kategori_id" class="select2 form-select" required onchange="toggleInput()">
                                    <option value="">Pilih Kategori Galeri</option>
                                    <option value="foto" <?= $galeri->category == 'foto' ? 'selected' : '' ?>>Foto</option>
                                    <option value="video" <?= $galeri->category == 'video' ? 'selected' : '' ?>>Video</option>
                                </select>
                            </div>
                        </div>

                        <!-- Input Gambar -->
                        <div class="col-md-6" id="imageFileContainer" style="display: <?= $galeri->category == 'foto' ? 'block' : 'none' ?>;">
                            <div class="mb-3">
                                <label for="gambar" class="form-label">Upload Gambar <span class="text-danger">*(maks 2MB)</span></label>
                                <input type="file" name="gambar" id="gambar" class="form-control" accept="image/*">
                                <!-- Area untuk pratinjau gambar -->
                                <div id="preview-container" class="mt-3" style="display: <?= !empty($galeri->picture) && $galeri->category == 'foto' ? 'block' : 'none' ?>;">
                                    <img id="image-preview" src="<?= base_url('uploads/gallery/' . $galeri->picture) ?>" alt="Pratinjau"
                                        style="max-width: 100%; max-height: 200px;">
                                </div>
                                <small>*Unggah file gambar (format: JPG/PNG/JPEG).</small>
                            </div>
                        </div>

                        <!-- Input Link -->
                        <div class="col-md-6" id="UrlContainer" style="display: <?= $galeri->category == 'video' ? 'block' : 'none' ?>;">
                            <div class="mb-3">
                                <label for="link" class="form-label">Link Video <span class="text-danger">*</span></label>
                                <input type="text" name="link" id="link" class="form-control" value="<?= $galeri->category == 'video' ? $galeri->link : '' ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end mt-4">
                <a href="<?= base_url('master/gallery') ?>" class="btn btn-danger">
                    <i class="ti ti-arrow-back me-2"></i> Kembali
                </a>

                <button type="submit" class="btn btn-primary ms-2">
                    <i class="ti ti-check me-2"></i> Simpan
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmGallery', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, (ok) => {
                        return window.location.href = '<?= base_url('master/gallery') ?>';
                    });
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });

        // Fungsi toggle input berdasarkan kategori
        toggleInput();
    };

    function toggleInput() {
        const type = document.getElementById('kategori_id').value;
        const imageFileContainer = document.getElementById('imageFileContainer');
        const fileInput = document.getElementById('gambar');
        const UrlContainer = document.getElementById('UrlContainer');
        const UrlInput = document.getElementById('link');
        const previewContainer = document.getElementById('preview-container');
        const imagePreview = document.getElementById('image-preview');

        if (type === 'foto') {
            // Tampilkan form gambar
            imageFileContainer.style.display = 'block';
            fileInput.setAttribute('required', true);

            // Sembunyikan form link
            UrlContainer.style.display = 'none';
            UrlInput.removeAttribute('required');
            UrlInput.value = '';

        } else if (type === 'video') {
            // Tampilkan form link
            UrlContainer.style.display = 'block';
            UrlInput.setAttribute('required', true);
            UrlInput.value = '<?= $galeri->category == "video" ? $galeri->link : "" ?>';

            // Sembunyikan form gambar dan preview
            imageFileContainer.style.display = 'none';
            fileInput.removeAttribute('required');
            fileInput.value = '';
            previewContainer.style.display = 'none';

        } else {
            // Sembunyikan semua
            imageFileContainer.style.display = 'none';
            UrlContainer.style.display = 'none';
            fileInput.removeAttribute('required');
            UrlInput.removeAttribute('required');
        }
    }

    // Fungsi untuk preview gambar saat dipilih
    document.getElementById('gambar').addEventListener('change', function(event) {
        const file = event.target.files[0];
        const previewContainer = document.getElementById('preview-container');
        const imagePreview = document.getElementById('image-preview');

        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.src = e.target.result;
                previewContainer.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }
    });

    // Pasang event listener untuk perubahan kategori
    document.getElementById('kategori_id').addEventListener('change', toggleInput);

    // Panggil fungsi toggle saat halaman dimuat
</script>