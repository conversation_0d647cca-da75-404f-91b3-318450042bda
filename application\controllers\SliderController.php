<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsSlider $msslider
 * @property CI_DB_query_builder $db
 * @property Datatables $datatables
 */
class SliderController extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsSlider', 'msslider');
    }

    public function index()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Profil Sekolah - Slider';
        $data['content'] = 'admin/schoolprofile/slider/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('MsSlider', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.createdby'] = getCurrentIdUser();

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $detail[] = '<img src="' . base_url('uploads/slider/' . $value->picture) . '" style="width: 150px; height: 100px; object-fit: cover; object-position: center; border-radius: 8px;" class="img-fluid">';
            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('master/slider/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"deleteSlider('" . $value->id .  "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                    <i class=\"ti ti-trash\"></i>
                </button>
            </div>";

            $detail[] = $actions;
            $data[] = $detail;
        }
        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Profil Sekolah - Tambah Slider';
        $data['content'] = 'admin/schoolprofile/slider/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $picture = $_FILES['picture'];

        $picture_path = null;
        if ($picture['size'] > 0) {
            $config['upload_path'] = './uploads/slider/';
            $config['allowed_types'] = 'jpg|jpeg|png|webp';
            $config['max_size'] = 3048;
            $config['encrypt_name'] = true;

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('picture')) {
                return JSONResponseDefault('FAILED', 'Pastikan ukuran file tidak lebih dari 3 Mb');
            }

            $upload_data = $this->upload->data();
            $picture_path =  $upload_data['file_name'];
        }

        $insert = array(
            'picture' => $picture_path,
            'createddate' => getCurrentDate(),
            'createdby' => getCurrentIdUser(),
        );

        $insert_result = $this->msslider->insert($insert);

        if ($insert_result) {
            return JSONResponseDefault('OK', 'Data Slider berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Data Slider gagal ditambahkan');
        }
    }

    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        };

        $slider = $this->msslider->get(array('id' => $id));

        if ($slider->num_rows() == 0) {
            redirect('master/news');
        }

        $data = array();
        $data['slider'] = $slider->row();
        $data['title'] = 'Profil Sekolah - Ubah Slider';
        $data['content'] = 'admin/schoolprofile/slider/edit';

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $slider = $this->msslider->get(array('id' => $id));

        if ($slider->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $slider_data = $slider->row();
        $picture = $_FILES['picture'];

        $update = array();
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        if ($picture['size'] > 0) {
            $config['upload_path'] = './uploads/slider/';
            $config['allowed_types'] = 'jpg|jpeg|png|webp';
            $config['max_size'] = 3048;
            $config['encrypt_name'] = true;

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('picture')) {
                return JSONResponseDefault('FAILED', 'Pastikan Ukuran file tidak lebih dari 3 mb');
            }


            if (!empty($slider_data->picture) && file_exists('./uploads/slider/' . $slider_data->picture)) {
                unlink('./uploads/slider/' . $slider_data->picture);
            }

            $upload_data = $this->upload->data();
            $update['picture'] = $upload_data['file_name'];
        }

        $update_result = $this->msslider->update(array('id' => $id), $update);

        if ($update_result) {
            return JSONResponseDefault('OK', 'Data Slider Berhasil Diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Data Slider Gagal Diubah');
        }
    }

    public function process_delete()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda Tidak Memiliki akses!');
        }

        $id = getPost('id');

        if (empty($id)) {
            return JSONResponseDefault('FAILED', 'Data Tidak Ditemukan');
        }

        $slider = $this->msslider->get(array('id' => $id));

        if ($slider->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $slider_data = $slider->row();

        if (!empty($slider_data->picture) && file_exists('./uploads/slider/' . $slider_data->picture)) {
            unlink('./uploads/slider/' . $slider_data->picture);
        }

        $delete = $this->msslider->delete(array('id' => $id));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data Slider berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Data Slider gagal dihapus');
        }
    }
}
