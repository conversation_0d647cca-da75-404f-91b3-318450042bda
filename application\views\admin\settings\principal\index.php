<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<h4 class="py-3 mb-4">
    <span class="text-muted fw-light">Profil <PERSON> /</span> Kepala Sekolah
</h4>
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <h5 class="card-header">Formulir Pengaturan Kepala Sekolah</h5>
            <!-- Account -->
            <form id="frmSettingsPrincipal" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                <div class="card-body pt-0">
                    <div class="d-flex align-items-center flex-column">
                        <?php if ($introduction != null && $introduction->picture != null && file_exists('./uploads/principal/' . $introduction->picture)) : ?>
                            <img src="<?= base_url('uploads/principal/') . $introduction->picture ?>" alt="user-avatar" class="d-block w-px-100 h-px-100 rounded" id="uploadedAvatar" />
                        <?php else : ?>
                            <img src="<?= base_url() ?>/assets/img/avatars/14.png" alt="user-avatar" class="d-block w-px-100 h-px-100 rounded" id="uploadedAvatar" />
                        <?php endif; ?>

                        <div class="text-muted mt-3">Format JPG, JPEG, PNG</div>
                        <div class="button-wrapper mt-3">
                            <label for="upload" class="btn btn-primary me-2 mb-3" tabindex="0">
                                <span class="d-none d-sm-block">Unggah Foto Profil</span>
                                <i class="ti ti-upload d-block d-sm-none"></i>
                                <input type="file" name="profile" id="upload" class="account-file-input" hidden accept="image/png, image/jpeg, image/jpg" />
                            </label>
                            <button type="button" class="btn btn-label-secondary account-image-reset mb-3">
                                <i class="ti ti-refresh-dot d-block d-sm-none"></i>
                                <span class="d-none d-sm-block">Reset</span>
                            </button>
                        </div>
                    </div>
                </div>

                <hr class="my-0">

                <div class="card-body">
                    <div class="row">
                        <div class="mb-3 col-md-12">
                            <label for="principal" class="form-label">Nama Kepala Sekolah<span class="text-danger">*</span></label>
                            <input class="form-control" type="text" name="principal" id="principal" placeholder="Masukkan Nama Kepala Sekolah" value="<?= $settings->principal ?? null ?>" required />
                        </div>
                        <div class="mb-3 col-md-12">
                            <label for="intro" class="form-label">Kata Pengantar<span class="text-danger">*</span></label>
                            <div id="introduction">
                                <?= $introduction->introduction ?? null ?>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-end mt-">
                        <button type="submit" class="btn btn-primary ms-2">
                            <i class="ti ti-check me-2"></i>
                            Simpan
                        </button>
                    </div>
                </div>
            </form>
            <!-- /Account -->
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        let e = document.getElementById("uploadedAvatar");
        const l = document.querySelector(".account-file-input"),
            c = document.querySelector(".account-image-reset");
        if (e) {
            const r = e.src;
            l.onchange = () => {
                l.files[0] && (e.src = window.URL.createObjectURL(l.files[0]))
            }, c.onclick = () => {
                l.value = "", e.src = r
            }
        }

        let introductionEditor = new Quill("#introduction", {
            bounds: "#introduction",
            placeholder: "Ketik kata pengantar di sini...",
            modules: {
                formula: true,
                toolbar: [
                    [{
                        font: []
                    }, {
                        size: []
                    }],
                    ["bold", "italic", "underline", "strike"],
                    [{
                        color: []
                    }, {
                        background: []
                    }],
                    [{
                        script: "super"
                    }, {
                        script: "sub"
                    }],
                    [{
                        header: "1"
                    }, {
                        header: "2"
                    }, "blockquote", "code-block"],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }, {
                        indent: "-1"
                    }, {
                        indent: "+1"
                    }],
                    [{
                        direction: "rtl"
                    }],
                    ["link", "image", "video", "formula"],
                    ["clean"]
                ]
            },
            theme: "snow"
        });

        const maxWidth = 600;
        const maxHeight = 600;

        let toolbar = introductionEditor.getModule('toolbar');
        toolbar.addHandler('image', function() {
            const input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');

            input.click();

            input.onchange = function() {
                const file = input.files[0];

                if (file) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        const img = new Image();

                        img.onload = function() {
                            // Memeriksa dimensi gambar
                            if (img.width > maxWidth || img.height > maxHeight) {
                                swalMessageFailed(`Gambar terlalu besar. Maksimal dimensi adalah ${maxWidth}x${maxHeight} px.`);
                                return;
                            }

                            const range = introductionEditor.getSelection();
                            introductionEditor.insertEmbed(range.index, 'image', e.target.result);
                        };

                        img.src = e.target.result;
                    };

                    reader.readAsDataURL(file);
                }
            };
        });

        $('#frmSettingsPrincipal').submit(function(e) {
            e.preventDefault();
            $('button[type="submit"]').attr("disabled", true);

            let introduction = introductionEditor.root.innerHTML;

            if (!introduction || introduction === "<p><br></p>" || introduction === "") {
                swalMessageFailed("Isi Kata Pengantar tidak boleh kosong");
                $('button[type="submit"]').removeAttr("disabled");
                return;
            }

            let formData = new FormData(this);
            formData.append("introduction", introduction);

            $.ajax({
                url: $(this).attr("action"),
                type: "POST",
                dataType: "JSON",
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.RESULT === "OK") {
                        swalMessageSuccess(response.MESSAGE, (ok) => {
                            $('button[type="submit"]').removeAttr("disabled");
                            window.location.reload();
                        });
                    } else {
                        swalMessageFailed(response.MESSAGE);
                        $('button[type="submit"]').removeAttr("disabled");
                    }
                },
                error: function() {
                    $('button[type="submit"]').removeAttr("disabled");
                    swalError();
                }
            });
        });
    }

    function cleanQuillContent(content) {
        return (content.trim() === '<p><br></p>') ? '' : content;
    }
</script>