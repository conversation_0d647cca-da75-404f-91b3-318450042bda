<?php

use PhpOffice\PhpSpreadsheet\Calculation\TextData\Trim;
use PhpOffice\PhpSpreadsheet\Writer\Ods\Thumbnails;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsArchive $msarchive
 * @property MsCategoryArchive $mscategoryarchive
 * @property CI_DB_query_builder $db
 * @property Datatables $datatables
 */
class ArchiveController extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->model('MsArchive', 'msarchive');
        $this->load->model('MsCategoryArchive', 'mscategoryarchive');
    }

    public function index()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect('auth/login/admin');
        }

        $data = array();
        $data['title'] = 'Arsip Sekolah - Arsip';
        $data['content'] = 'admin/master/archive/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('MsArchive', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.createdby'] = getCurrentIdUser();
        $where['a.isdeleted'] = null;

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $detail[] = $value->archive_number;
            $detail[] = $value->category_name;
            $detail[] = '<div style="max-width: 300px; word-wrap: break-word; white-space: normal;">' . htmlspecialchars($value->description) . '</div>';
            $detail[] = '<a href="' . base_url('uploads/archive/' . $value->document) . '" download>' . basename($value->document) . '</a>';
            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('master/archive/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"deleteArchive('" . $value->id .  "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                    <i class=\"ti ti-trash\"></i>
                </button>
            </div>";
            $detail[] = $actions;
            $data[] = $detail;
        }
        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['kategori'] = $this->mscategoryarchive->get(array('createdby' => getCurrentIdUser()))->result();
        $data['title'] = 'Arsip Sekolah - Tambah Arsip';
        $data['content'] = 'admin/master/archive/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $archive_N = trim(getPost('n_archive'));
        $categoryid = getPost('kategori_id');
        $description = getPost('deskripsi');
        $document = $_FILES['document'] ?? null;


        if (empty($archive_N)) {
            return JSONResponseDefault('FAILED', 'Nomor arsip tidak boleh kosong');
        } elseif (empty($categoryid)) {
            return JSONResponseDefault('FAILED', 'Kategori arsip tidak boleh kosong');
        } elseif (empty($description)) {
            return JSONResponseDefault('FAILED', 'Deskripsi arsip tidak boleh kosong');
        }

        $query = $this->msarchive->get(array('archive_number' => $archive_N), 1);
        if ($query->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Nomor arsip sudah ada');
        }
        // Validasi file dokumen
        $document_path = null;
        if (!empty($document) && $document['size'] > 0) {
            $upload_path = './uploads/archive/';
            $allowed_types = 'pdf|doc|docx|xls|xlsx';
            $max_size = 10200;

            // Mendapatkan nama asli file
            $original_file_name = pathinfo($document['name'], PATHINFO_FILENAME);
            $file_extension = pathinfo($document['name'], PATHINFO_EXTENSION);
            $new_file_name = $original_file_name . '_' . time() . '.' . $file_extension;


            $config = [
                'upload_path'   => $upload_path,
                'allowed_types' => $allowed_types,
                'max_size'      => $max_size,
                'file_name'     => $new_file_name,
            ];


            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('document')) {
                return JSONResponseDefault('FAILED', 'Pastikan ukuran file tidak lebih dari 10 MB ');
            }

            $upload_data = $this->upload->data();
            $document_path = $upload_data['file_name'];
        }


        // Masukkan data ke database
        $insert = [
            'archive_number' => $archive_N,
            'categoryid' => $categoryid,
            'description' => $description,
            'document' => $document_path,
            'createddate' => getCurrentDate(),
            'createdby' => getCurrentIdUser(),
        ];

        $insert_result = $this->msarchive->insert($insert);

        if ($insert_result) {
            return JSONResponseDefault('OK', 'Data Arsip berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Data Arsip gagal ditambahkan');
        }
    }

    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $archive = $this->msarchive->get(array('id' => $id));

        if ($archive->num_rows() == 0) {
            redirect('master/archive');
        }

        $data = array();
        $data['kategori'] = $this->mscategoryarchive->get(array('createdby' => getCurrentIdUser()))->result();
        $data['archive'] = $archive->row();
        $data['title'] = 'Arsip Sekolah - Ubah Arsip';
        $data['content'] = 'admin/master/archive/edit';

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $arsip = $this->msarchive->get(array('id' => $id, 'createdby' => getCurrentIdUser()));

        if ($arsip->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $archive_N = trim(getPost('n_archive'));
        $categoryid = getPost('kategori_id');
        $description = getPost('deskripsi');
        $document = $_FILES['document'] ?? null;

        if (empty($archive_N)) {
            return JSONResponseDefault('FAILED', 'Nomor arsip tidak boleh kosong');
        } else if (empty($categoryid)) {
            return JSONResponseDefault('FAILED', 'Kategori arsip tidak boleh kosong');
        } else if (empty($description)) {
            return JSONResponseDefault('FAILED', 'Deskripsi arsip tidak boleh kosong');
        }

        $ceknumber = $this->msarchive->get(array('archive_number' => $archive_N, 'id !=' => $id));

        if ($ceknumber->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Nomor arsip sudah digunakan');
        }

        $update = array();
        $update['archive_number'] = $archive_N;
        $update['categoryid'] = $categoryid;
        $update['description'] = $description;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $old_document = $arsip->row()->document;

        if ($document['size'] > 0) {
            $upload_path = './uploads/archive/';
            $allowed_types = 'pdf|doc|docx|xls|xlsx';
            $max_size = 10200;  // 10 MB

            $original_file_name = pathinfo($document['name'], PATHINFO_FILENAME);
            $file_extension = pathinfo($document['name'], PATHINFO_EXTENSION);
            $new_file_name = $original_file_name . '_' . time() . '.' . $file_extension;


            $config = [
                'upload_path'   => $upload_path,
                'allowed_types' => $allowed_types,
                'max_size'      => $max_size,
                'file_name'     => $new_file_name,
            ];

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('document')) {
                return JSONResponseDefault('FAILED', 'Pastikan ukuran file tidak lebih dari 10 MB');
            }

            $upload_data = $this->upload->data();
            $update['document'] = $upload_data['file_name'];

            // Hapus dokumen lama jika ada
            if (!empty($old_document) && file_exists('./uploads/archive/' . $old_document)) {
                unlink('./uploads/archive/' . $old_document);
            }
        }

        $update_result = $this->msarchive->update(array('id' => $id), $update);

        if ($update_result) {
            return JSONResponseDefault('OK', 'Data arsip berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Data arsip gagal diubah');
        }
    }

    public function process_delete()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $id = getPost('id');

        if (empty($id)) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $arsip = $this->msarchive->get(array('id' => $id));

        if ($arsip->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $document = $arsip->row()->document;

        if (!empty($document)) {
            $filePath = './uploads/archive/' . $document;

            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }

        $delete = $this->msarchive->delete(array('id' => $id));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data arsip berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Data arsip gagal dihapus');
        }
    }
}
