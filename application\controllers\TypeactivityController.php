<?php
defined('BASEPATH') or die('No direct script access allowed!');

class TypeactivityController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('MsActivity', 'msactivity');
        $this->load->model('MsTypeactivity', 'mstypeactivity');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Agenda - Jenis Kegiatan';
        $data['content'] = 'admin/master/typeactivity/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $datatable = $this->datatables->make('MsTypeactivity', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.createdby'] = getCurrentIdUser();

        $data = array();
        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $detail[] = $value->name;
            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('master/typeactivity/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                    
                   <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"deleteCactivity('" . $value->id . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                    <i class=\"ti ti-trash\"></i>
                </button>
                  
            </div>";
            $detail[] = $actions;
            $data[] = $detail;
        }
        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Agenda - Tambah Kegiatan';
        $data['content'] = 'admin/master/typeactivity/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        // Periksa apakah user login dan memiliki akses sebagai admin
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        // Ambil data dari input
        $name = trim(getPost('name'));

        // Validasi input
        if (empty($name)) {
            return JSONResponseDefault('FAILED', 'Nama jenis kegiatan tidak boleh kosong');
        }

        // Periksa apakah nama sudah ada dalam database
        $cekNama = $this->mstypeactivity->get(array(
            'UPPER(name)' => strtoupper($name),
            'createdby' => getCurrentIdUser()
        ))->num_rows();
        if ($cekNama > 0) {
            return JSONResponseDefault('FAILED', 'Nama kategori sudah digunakan');
        }

        // Data yang akan ditambahkan ke database
        $insert = [
            'name' => $name,
            'createdby' => getCurrentIdUser(), // Tambahkan jika ada relasi ke user
            'createddate' => getCurrentDate(), // Tambahkan tanggal pembuatan
        ];

        $insert = $this->mstypeactivity->insert($insert);

        // Proses penyimpanan ke database
        if ($insert) {
            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Data gagal ditambahkan');
        }
    }


    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        // Ambil data activity berdasarkan ID
        $type = $this->mstypeactivity->get(array(
            'id' => $id
        ));

        if ($type->num_rows() == 0) {
            return redirect(base_url('master/typeactivity'));
        }

        $data['title'] = 'Agenda - Ubah Jenis Kegiatan';
        $data['content'] = 'admin/master/typeactivity/edit';
        $data['type'] = $type->row();

        return $this->load->view('master', $data);
    }
    public function process_edit($id)
    {
        $jenis = $this->mstypeactivity->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }
        if ($jenis->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        // Ambil input nama dari POST
        $name = trim(getPost('name'));
        if (empty($name)) {
            return JSONResponseDefault('FAILED', 'Nama jenis kegiatan tidak boleh kosong');
        }

        // Periksa apakah nama sudah digunakan
        $cekjenis = $this->mstypeactivity->get(array(
            'UPPER(name)' => strtoupper($name),
            'id !=' => $id
        ))->num_rows();

        if ($cekjenis > 0) {
            return JSONResponseDefault('FAILED', 'Kategori sudah digunakan');
        }

        // Siapkan data yang akan diupdate
        $update = array();
        $update['name'] = $name;
        $update['updatedby'] = getCurrentIdUser();
        $update['updateddate'] = getCurrentDate();

        $update_result = $this->mstypeactivity->update(array('id' => $id), $update);

        if ($update_result) {
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Data gagal diubah');
        }
    }

    public function delete()
    {
        // Memulai transaksi database
        $this->db->trans_begin();

        // Periksa apakah user login dan memiliki akses sebagai admin
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        // Ambil ID dari input POST
        $id = getPost('id');

        if (empty($id)) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        // Periksa apakah jenis kegiatan dengan ID ini ada dan dimiliki oleh user saat ini
        $jenis = $this->mstypeactivity->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($jenis->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data jenis kegiatan tidak ditemukan');
        }

        $this->msactivity->delete(array('type' => $id));

        // Hapus data dari tabel typeactivity
        $this->mstypeactivity->delete(array('id' => $id));

        // Periksa status transaksi
        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Data jenis kegiatan gagal dihapus');
        } else {
            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        }
    }
}
