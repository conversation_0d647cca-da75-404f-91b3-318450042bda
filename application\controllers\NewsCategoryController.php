<?php

use PhpOffice\PhpSpreadsheet\Calculation\MathTrig\Arabic;
use PhpOffice\PhpSpreadsheet\Writer\Ods\Thumbnails;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsCategoryNews $mscategorynews
 * @property MsNews $msnews
 * @property CI_DB_query_builder $db
 * @property Datatables $datatables
 */
class NewsCategoryController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsCategoryNews', 'mscategorynews');
        $this->load->model('MsNews', 'msnews');
    }

    public function index()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Berita Sekolah - Kategori Berita';
        $data['content'] = 'admin/master/categorynews/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('MsCategoryNews', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.createdby'] = getCurrentIdUser();
        $where['a.isdeleted'] = null;

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $detail[] = $value->name;
            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('master/categorynews/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"deleteCategoryNews('" . $value->id .  "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                    <i class=\"ti ti-trash\"></i>
                </button>
            </div>";

            $detail[] = $actions;
            $data[] = $detail;
        }
        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect('auth/login/admin');
        }

        $data = array();
        $data['title'] = 'Berita Sekolah - Tambah Kategori Berita';
        $data['content'] = 'admin/master/categorynews/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $name = trim(getPost('name'));

        if (empty($name)) {
            return JSONResponseDefault('FAILED', 'Nama Kategori tidak boleh kosong');
        }

        $cek = $this->mscategorynews->get(array(
            'name' => $name,
            'createdby' => getCurrentIdUser()
        ));

        if ($cek->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Kategori sudah digunakan');
        }

        $insert = array();
        $insert['name'] = $name;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $insert = $this->mscategorynews->insert($insert);

        if ($insert) {
            return JSONResponseDefault('OK', 'Data Kategori Berita berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', "DATA Kategori Berita Gagal ditambahkan");
        }
    }

    public function edit($id)
    {

        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $kategori = $this->mscategorynews->get(array('id' => $id));

        if ($kategori->num_rows() == 0) {
            return redirect('master/categorynews');
        }

        $data = array();
        $data['title'] = 'Berita Sekolah - Ubah Kategori';
        $data['content'] = 'admin/master/categorynews/edit';
        $data['kategori'] = $kategori->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $kategori = $this->mscategorynews->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($kategori->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $name = trim(getPost('name'));

        if (empty($name)) {
            return JSONResponseDefault('FAILED', 'Nama Kategori tidak boleh kosong');
        }

        $cekkategori = $this->mscategorynews->get(array(
            'UPPER(name)' => strtoupper($name),
            'id !=' => $id
        ));

        if ($cekkategori->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Kategori sudah digunakan');
        }

        $update = array();
        $update['name'] = $name;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();


        $update = $this->mscategorynews->update(array('id' => $id), $update);

        if ($update) {
            return JSONResponseDefault('OK', 'Data Kategori berita berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Data Kategori berita gagal diubah');
        }
    }

    public function process_delete()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = getPost('id');

            if (empty($id)) {
                throw new Exception('Data Tidak Ditemukan');
            }

            $getCategory = $this->mscategorynews->get(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($getCategory->num_rows() == 0) {
                throw new Exception('Data Kategori Tidak ditemukan');
            }

            $news = $this->msnews->get(array('categoryid' => $id))->result();

            foreach ($news as $item) {
                $filePath = './uploads/news/' . $item->thumbnail;
                if (file_exists($filePath) && !empty($item->thumbnail)) {
                    unlink($filePath);
                }
            }

            // Hapus kategori berita
            $this->mscategorynews->delete(array('id' => $id));

            // Hapus berita terkait
            $this->msnews->delete(array('categoryid' => $id));

            if ($this->db->trans_status() === FALSE) {
                $this->db->trans_rollback();
                throw new Exception('Data Kategori berita gagal dihapus');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data Kategori Berita dan berita terkait berhasil dihapus');
            }
        } catch (Exception $e) {
            // Jika terjadi exception, rollback transaksi dan beri pesan error
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }
}
