<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<h4 class="py-3 mb-4">
    <span class="text-muted fw-light">Pengaturan /</span> Sekolah
</h4>
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <h5 class="card-header">Formulir Pengaturan Sekolah</h5>
            <!-- Account -->
            <form id="frmSettingsGeneral" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                <div class="card-body pt-0">
                    <div class="row">
                        <div class="col-md-6 mt-3">
                            <div class="d-flex align-items-center flex-column">
                                <?php if ($settings->logo != null && file_exists('./uploads/logo/' . $settings->logo)) : ?>
                                    <img src="<?= base_url('uploads/logo/') . $settings->logo ?>" alt="user-avatar" class="d-block w-px-100 h-px-100 rounded" id="uploadedAvatar" />
                                <?php else : ?>
                                    <img src="<?= base_url() ?>/assets/img/avatars/14.png" alt="user-avatar" class="d-block w-px-100 h-px-100 rounded" id="uploadedAvatar" />
                                <?php endif; ?>

                                <div class="text-muted mt-3">Logo Sekolah</div>
                                <div class="button-wrapper mt-3">
                                    <label for="upload" class="btn btn-primary me-2 mb-3" tabindex="0">
                                        <span class="d-none d-sm-block">Unggah</span>
                                        <i class="ti ti-upload d-block d-sm-none"></i>
                                        <input type="file" name="logo" id="upload" class="account-file-input" hidden accept="image/png, image/jpeg, image/jpg" />
                                    </label>
                                    <button type="button" class="btn btn-label-secondary account-image-reset mb-3">
                                        <i class="ti ti-refresh-dot d-block d-sm-none"></i>
                                        <span class="d-none d-sm-block">Reset</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mt-3">
                            <div class="d-flex align-items-center flex-column">
                                <?php if ($settings->logo_institution != null && file_exists('./uploads/logo/' . $settings->logo_institution)) : ?>
                                    <img src="<?= base_url('uploads/logo/') . $settings->logo_institution ?>" alt="user-avatar" class="d-block w-px-100 h-px-100 rounded" id="uploadedAvatar-2" />
                                <?php else : ?>
                                    <img src="<?= base_url() ?>/assets/img/avatars/14.png" alt="user-avatar" class="d-block w-px-100 h-px-100 rounded" id="uploadedAvatar-2" />
                                <?php endif; ?>

                                <div class="text-muted mt-3">Lembaga Pendidikan</div>
                                <div class="button-wrapper mt-3">
                                    <label for="upload_institution" class="btn btn-primary me-2 mb-3" tabindex="0">
                                        <span class="d-none d-sm-block">Unggah</span>
                                        <i class="ti ti-upload d-block d-sm-none"></i>
                                        <input type="file" name="logo_institution" id="upload_institution" class="account-file-input-2" hidden accept="image/png, image/jpeg, image/jpg" />
                                    </label>
                                    <button type="button" class="btn btn-label-secondary account-image-reset-2 mb-3">
                                        <i class="ti ti-refresh-dot d-block d-sm-none"></i>
                                        <span class="d-none d-sm-block">Reset</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <hr class="my-0">

                <div class="card-body">
                    <div class="row">
                        <div class="mb-3 col-md-12">
                            <label for="schoolname" class="form-label">Nama Sekolah<span class="text-danger">*</span></label>
                            <input class="form-control" type="text" id="schoolname" name="schoolname" placeholder="Masukkan Nama Sekolah" value="<?= $settings->name ?? null ?>" required />
                        </div>
                        <div class="mb-3 col-md-6">
                            <label for="npsn" class="form-label">NPSN<span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="npsn" name="npsn" placeholder="Masukkan NPSN" value="<?= $settings->npsn ?? null ?>" required />
                        </div>

                        <div class="mb-3 col-md-6">
                            <label for="provinceid" class="form-label">Provinsi</label>
                            <select id="provinceid" name="provinceid" class="select2 form-select" onchange="changeProvince(this)" required>
                                <option value="">Pilih salah satu</option>
                                <?php foreach ($province as $value) : ?>
                                    <option value="<?= $value->id ?>" <?= $settings->provinceid == $value->id ? 'selected' : null ?>><?= $value->name ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3 col-md-6">
                            <label for="cityid" class="form-label">Kabupaten/Kota</label>
                            <select id="cityid" name="cityid" class="select2 form-select" onchange="changeCity(this)" required>
                                <option value="">Pilih salah satu</option>
                                <?php foreach ($city as $value) : ?>
                                    <option value="<?= $value->id ?>" <?= $settings->cityid == $value->id ? 'selected' : null ?>><?= $value->name ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3 col-md-6">
                            <label for="districtid" class="form-label">Kecamatan</label>
                            <select id="districtid" name="districtid" class="select2 form-select" onchange="changeDistrict(this)" required>
                                <option value="">Pilih salah satu</option>
                                <?php foreach ($district as $value) : ?>
                                    <option value="<?= $value->id ?>" <?= $settings->districtid == $value->id ? 'selected' : null ?>><?= $value->name ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3 col-md-12">
                            <label for="subdistrictid" class="form-label">Desa</label>
                            <select id="subdistrictid" name="subdistrictid" class="select2 form-select" required>
                                <option value="">Pilih salah satu</option>
                                <?php foreach ($subdistrict as $value) : ?>
                                    <option value="<?= $value->id ?>" <?= $settings->subdistrictid == $value->id ? 'selected' : null ?>><?= $value->name ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3 col-md-6">
                            <label for="phonenumber" class="form-label">Telepon</label>
                            <input class="form-control" type="text" name="phonenumber" id="phonenumber" placeholder="Masukkan Telepon Sekolah" value="<?= $settings->phonenumber ?? null ?>" required />
                        </div>

                        <div class="mb-3 col-md-6">
                            <label for="schoolemail" class="form-label">Email</label>
                            <input class="form-control" type="email" name="schoolemail" id="schoolemail" placeholder="Masukkan Email Sekolah" value="<?= $settings->schoolemail ?? null ?>" required />
                        </div>

                        <div class="mb-3 col-md-12">
                            <label for="address" class="form-label">Alamat</label>
                            <textarea name="address" class="form-control" id="address" cols="30" rows="10" placeholder="Masukkan Alamat" required><?= $settings->address ?? null ?></textarea>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end mt-4">
                        <button type="submit" class="btn btn-primary ms-2">
                            <i class="ti ti-check me-2"></i>
                            Simpan
                        </button>
                    </div>
                </div>
            </form>
            <!-- /Account -->
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmSettingsGeneral', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('button[type="submit"]').removeAttr('disabled');
                    return swalMessageSuccess(response.MESSAGE, (ok) => {
                        return window.location.reload();
                    });
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });

        let e = document.getElementById("uploadedAvatar");
        const l = document.querySelector(".account-file-input"),
            c = document.querySelector(".account-image-reset");
        if (e) {
            const r = e.src;
            l.onchange = () => {
                l.files[0] && (e.src = window.URL.createObjectURL(l.files[0]))
            }, c.onclick = () => {
                l.value = "", e.src = r
            }
        }

        let institue = document.getElementById("uploadedAvatar-2");
        const l2 = document.querySelector(".account-file-input-2"),
            c2 = document.querySelector(".account-image-reset-2");
        if (institue) {
            const r2 = institue.src;
            l2.onchange = () => {
                l2.files[0] && (institue.src = window.URL.createObjectURL(l2.files[0]))
            }, c2.onclick = () => {
                l2.value = "", institue.src = r2
            }
        }
    }

    function changeProvince(these) {
        let provinceid = these.value;
        $.ajax({
            url: '<?= base_url('select/city') ?>',
            type: 'POST',
            data: {
                provinceid: provinceid
            },
            success: function(response) {
                $('#cityid').html(response);
                changeCity(document.getElementById('cityid'));
            }
        });
    }

    function changeCity(these) {
        let cityid = these.value;
        $.ajax({
            url: '<?= base_url('select/district') ?>',
            type: 'POST',
            data: {
                cityid: cityid
            },
            success: function(response) {
                $('#districtid').html(response);
                changeDistrict(document.getElementById('districtid'));
            }
        });
    }

    function changeDistrict(these) {
        let districtid = these.value;
        $.ajax({
            url: '<?= base_url('select/subdistrict') ?>',
            type: 'POST',
            data: {
                districtid: districtid
            },
            success: function(response) {
                $('#subdistrictid').html(response);
            }
        });
    }
</script>