<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<h4 class="py-3 mb-4">
    <span class="text-muted fw-light">Manajemen / Pengumuman / </span> Tambah Pengumuman
</h4>

<div class="row">
    <div class="col-md-12">
        <form id="frmAddAnnouncement" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <div class="card mb-3">
                <h5 class="card-header">Formulir Tambah Pengumuman</h5>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="classid" class="form-label">Kelas <span class="text-danger">*</span></label>
                                <select name="classid[]" id="classid" class="form-select" multiple required>
                                    <option value="All"><PERSON>li<PERSON></option>
                                    <?php foreach ($class as $key => $value) : ?>
                                        <option value="<?= $value->id ?>"><?= ($value->level ?? null) != null ? $value->level . ' - ' . $value->name : $value->name ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="description" class="form-label">Keterangan <span class="text-danger">*</span></label>
                                <div id="description"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end mt-4">
                <a href="<?= base_url('master/announcement') ?>" class="btn btn-danger">
                    <i class="ti ti-arrow-back me-2"></i>
                    Kembali
                </a>

                <button type="submit" class="btn btn-primary ms-2">
                    <i class="ti ti-check me-2"></i>
                    Simpan
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    window.onload = function() {
        new Quill("#description", {
            bounds: "#description",
            placeholder: "Type Something...",
            modules: {
                formula: !0,
                toolbar: [
                    [{
                        font: []
                    }, {
                        size: []
                    }],
                    ["bold", "italic", "underline", "strike"],
                    [{
                        color: []
                    }, {
                        background: []
                    }],
                    [{
                        script: "super"
                    }, {
                        script: "sub"
                    }],
                    [{
                        header: "1"
                    }, {
                        header: "2"
                    }, "blockquote", "code-block"],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }, {
                        indent: "-1"
                    }, {
                        indent: "+1"
                    }],
                    [{
                        direction: "rtl"
                    }],
                    ["link", "formula"],
                    ["clean"]
                ]
            },
            theme: "snow"
        });

        $('#classid').select2({
            placeholder: 'Pilih Kelas',
            width: '100%',
            allowClear: true
        });

        $('#classid').on('change', function() {
            let selected = $(this).val();
            if (selected.includes('All')) {
                $("#classid > option").prop('selected', true);
                $("#classid > option[value='All']").prop('selected', false);
                $('#classid').trigger('change');
            }
        });

        $('#frmAddAnnouncement').submit(function(e) {
            e.preventDefault();
            $('button[type="submit"]').attr('disabled');

            let description = cleanQuillContent($('#description .ql-editor').html());
            let classid = $(this).find('select[name="classid[]"]').val();

            if (!description || description.trim() === '' || description === '<p><br></p>') {
                swalMessageFailed('Keterangan tidak boleh kosong');
                $('button[type="submit"]').removeAttr('disabled');
                return;
            }

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                dataType: 'JSON',
                data: {
                    classid: classid,
                    description: description
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        return swalMessageSuccess(response.MESSAGE, (ok) => {
                            return window.location.href = '<?= base_url('master/announcement') ?>';
                        });
                    } else {
                        $('#button[type="submit"]').removeAttr('disabled');
                        return swalMessageFailed(response.MESSAGE);
                    }
                },
                error: function() {
                    $('#button[type="submit"]').removeAttr('disabled');
                    return swalError();
                }
            });
        });
    };

    function cleanQuillContent(content) {
        content = content.trim();

        const strippedContent = content.replace(/(<([^>]+)>)/gi, "").trim();

        return strippedContent.length === 0 ? "" : content;
    }
</script>