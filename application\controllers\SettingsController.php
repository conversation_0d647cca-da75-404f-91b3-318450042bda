<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property TbSettings $tbsettings
 * @property MsAttendancetime $msattendancetime
 * @property MsUsers $msusers
 * @property MsProvince $msprovince
 * @property MsCity $mscity
 * @property MsDistrict $msdistrict
 * @property MsSubdistrict $mssubdistrict
 * @property CI_DB_query_builder $db
 * @property CI_Upload $upload
 * @property Datatables $datatables
 */
class SettingsController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('TbSettings', 'tbsettings');
        $this->load->model('MsAttendancetime', 'msattendancetime');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsProvince', 'msprovince');
        $this->load->model('MsCity', 'mscity');
        $this->load->model('MsDistrict', 'msdistrict');
        $this->load->model('MsSubdistrict', 'mssubdistrict');
    }

    public function holiday()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $get =  $this->tbsettings->get(array(
            'settings_name' => 'holiday',
            'createdby' => getCurrentIdUser()
        ))->row();

        $data = array();
        $data['title'] = 'Pengaturan Hari Libur';
        $data['content'] = 'admin/settings/holiday/index';
        $data['holiday'] = $get != null ? explode(',', $get->settings_value) : array();

        return $this->load->view('master', $data);
    }

    public function process_holiday()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $day = getPost('day');
            $day = implode(',', $day);

            $get =  $this->tbsettings->get(array(
                'settings_name' => 'holiday',
                'createdby' => getCurrentIdUser()
            ))->num_rows();

            if ($get > 0) {
                $this->tbsettings->update(array(
                    'settings_name' => 'holiday',
                    'createdby' => getCurrentIdUser()
                ), array('settings_value' => $day));
            } else {
                $this->tbsettings->insert(array(
                    'settings_name' => 'holiday',
                    'settings_value' => $day,
                    'createdby' => getCurrentIdUser()
                ));
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan data');
            } else {
                $this->db->trans_commit();

                return JSONResponseDefault('OK', 'Berhasil menyimpan data');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function attendance()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $entrytime =  $this->tbsettings->get(array(
            'settings_name' => 'attendance_entry_time',
            'createdby' => getCurrentIdUser()
        ))->row()->settings_value ?? null;

        $hometime =  $this->tbsettings->get(array(
            'settings_name' => 'attendance_home_time',
            'createdby' => getCurrentIdUser()
        ))->row()->settings_value ?? null;

        $latetolerancetime =  $this->tbsettings->get(array(
            'settings_name' => 'attendance_late_tolerance_time',
            'createdby' => getCurrentIdUser()
        ))->row()->settings_value ?? null;

        $attendancetype =  $this->tbsettings->get(array(
            'settings_name' => 'attendance_type',
            'createdby' => getCurrentIdUser()
        ))->row()->settings_value ?? null;

        $data = array();
        $data['title'] = 'Pengaturan Absensi';
        $data['content'] = 'admin/settings/attendance/index';
        $data['entrytime'] = $entrytime;
        $data['hometime'] = $hometime;
        $data['latetolerancetime'] = $latetolerancetime;
        $data['attendancetype'] = $attendancetype;

        return $this->load->view('master', $data);
    }

    public function datatables_attendance()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('MsAttendancetime', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.createdby'] = getCurrentIdUser();

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('settings/attendance/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"deleteAttendancetime('" . $value->id . "','" . $value->days . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                    <i class=\"ti ti-trash\"></i>
                </button>
            </div>";

            $detail[] = $value->days;
            $detail[] = $value->entry_time;
            $detail[] = ($value->late_tolerance_time ?? 0) . ' Menit';
            $detail[] = $value->home_time;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function process_attendance()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $entry_time = getPost('entry_time');
            $home_time = getPost('home_time');
            $latetolerance_time = getPost('latetolerance_time', 0);

            if ($entry_time == null) {
                throw new Exception('Jam masuk tidak boleh kosong');
            } else if ($latetolerance_time < 0) {
                throw new Exception('Toleransi terlambat tidak boleh kurang dari 0');
            } else if ($home_time == null) {
                throw new Exception('Jam pulang tidak boleh kosong');
            } else if ($entry_time > $home_time) {
                throw new Exception('Jam masuk tidak boleh lebih besar dari jam pulang');
            } else if ($entry_time == $home_time) {
                throw new Exception('Jam masuk tidak boleh sama dengan jam pulang');
            }

            if ($latetolerance_time > 0) {
                $entry_late = DateTime::createFromFormat('H:i', $entry_time);

                if ($entry_late == false) {
                    throw new Exception('Format jam masuk tidak valid');
                }

                $entry_late->modify('+' . $latetolerance_time . ' minutes');

                if ($entry_late->format('H:i') >= $home_time) {
                    throw new Exception('Toleransi terlambat tidak boleh lebih besar dari jam pulang');
                }
            }

            $cekentrytime =  $this->tbsettings->get(array(
                'settings_name' => 'attendance_entry_time',
                'createdby' => getCurrentIdUser()
            ))->row();

            if ($cekentrytime != null) {
                $this->tbsettings->update(array(
                    'settings_name' => 'attendance_entry_time',
                    'createdby' => getCurrentIdUser()
                ), array('settings_value' => $entry_time));
            } else {
                $this->tbsettings->insert(array(
                    'settings_name' => 'attendance_entry_time',
                    'settings_value' => $entry_time,
                    'createdby' => getCurrentIdUser()
                ));
            }

            $ceklattime =  $this->tbsettings->get(array(
                'settings_name' => 'attendance_late_tolerance_time',
                'createdby' => getCurrentIdUser()
            ))->row();

            if ($ceklattime != null) {
                $this->tbsettings->update(array(
                    'settings_name' => 'attendance_late_tolerance_time',
                    'createdby' => getCurrentIdUser()
                ), array('settings_value' => $latetolerance_time));
            } else {
                $this->tbsettings->insert(array(
                    'settings_name' => 'attendance_late_tolerance_time',
                    'settings_value' => $latetolerance_time,
                    'createdby' => getCurrentIdUser()
                ));
            }

            $cekhometime =  $this->tbsettings->get(array(
                'settings_name' => 'attendance_home_time',
                'createdby' => getCurrentIdUser()
            ))->row();

            if ($cekhometime != null) {
                $this->tbsettings->update(array(
                    'settings_name' => 'attendance_home_time',
                    'createdby' => getCurrentIdUser()
                ), array('settings_value' => $home_time));
            } else {
                $this->tbsettings->insert(array(
                    'settings_name' => 'attendance_home_time',
                    'settings_value' => $home_time,
                    'createdby' => getCurrentIdUser()
                ));
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan data');
            } else {
                $this->db->trans_commit();

                return JSONResponseDefault('OK', 'Berhasil menyimpan data');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function type_attendance()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $type = getPost('type');

            if ($type != 'everyday' && $type != 'choose') {
                throw new Exception('Tipe absensi tidak diketahui');
            }

            $get = $this->tbsettings->get(array(
                'settings_name' => 'attendance_type',
                'createdby' => getCurrentIdUser()
            ));

            $execute = array();
            $execute['settings_name'] = 'attendance_type';
            $execute['settings_value'] = $type;
            $execute['createdby'] = getCurrentIdUser();

            if ($get->num_rows() == 0) {
                $execute = $this->tbsettings->insert($execute);
            } else {
                $execute = $this->tbsettings->update(array(
                    'settings_name' => 'attendance_type'
                ), $execute);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan data');
            } else {
                $this->db->trans_commit();

                return JSONResponseDefault('OK', 'Berhasil menyimpan data');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function add_attendance()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Tambah Waktu Absensi';
        $data['content'] = 'admin/settings/attendance/add';

        return $this->load->view('master', $data);
    }

    public function process_add_attendance()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $days = getPost('days');
            $entrytime = getPost('entry_time');
            $hometime = getPost('home_time');
            $latetolerance_time = getPost('latetolerance_time', 0);

            if ($days == null) {
                throw new Exception('Hari tidak boleh kosong');
            } else if ($entrytime == null) {
                throw new Exception('Jam masuk tidak boleh kosong');
            } else if ($latetolerance_time < 0) {
                throw new Exception('Toleransi terlambat tidak boleh kurang dari 0');
            } else if ($hometime == null) {
                throw new Exception('Jam pulang tidak boleh kosong');
            } else if ($entrytime > $hometime) {
                throw new Exception('Jam masuk tidak boleh lebih besar dari jam pulang');
            } else if ($entrytime == $hometime) {
                throw new Exception('Jam masuk tidak boleh sama dengan jam pulang');
            }

            $entry_late = DateTime::createFromFormat('H:i', $entrytime);

            if ($entry_late == false) {
                throw new Exception('Format jam masuk tidak valid');
            }

            $entry_late->modify('+' . $latetolerance_time . ' minutes');

            if ($latetolerance_time > 0 && $entry_late->format('H:i') >= $hometime) {
                throw new Exception('Toleransi terlambat tidak boleh lebih besar dari jam pulang');
            }

            $cekdays = $this->msattendancetime->get(array(
                'days' => $days,
                'createdby' => getCurrentIdUser()
            ));

            if ($cekdays->num_rows() > 0) {
                throw new Exception('Waktu Absensi hari ' . $days . ' sudah ada');
            }

            $insert = array();
            $insert['days'] = $days;
            $insert['entry_time'] = $entrytime;
            $insert['home_time'] = $hometime;
            $insert['late_tolerance_time'] = $latetolerance_time;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->msattendancetime->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal ditambahkan');
            } else {
                $this->db->trans_commit();

                return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function edit_attendance($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $cek = $this->msattendancetime->get(array(
            'id' => $id
        ));

        if ($cek->num_rows() == 0) {
            return redirect(base_url('settings/attendance'));
        }

        $data = array();
        $data['title'] = 'Ubah Waktu Absensi';
        $data['content'] = 'admin/settings/attendance/edit';
        $data['attendancetime'] = $cek->row();

        return $this->load->view('master', $data);
    }

    public function process_edit_attendance($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $cek = $this->msattendancetime->get(array('id' => $id));

            if ($cek->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $days = getPost('days');
            $entrytime = date('H:i', strtotime(getPost('entry_time')));
            $hometime = date('H:i', strtotime(getPost('home_time')));
            $latetolerance_time = getPost('latetolerance_time', 0);

            if ($days == null) {
                throw new Exception('Hari tidak boleh kosong');
            } else if ($entrytime == null) {
                throw new Exception('Jam masuk tidak boleh kosong');
            } else if ($latetolerance_time < 0) {
                throw new Exception('Toleransi terlambat tidak boleh kurang dari 0');
            } else if ($hometime == null) {
                throw new Exception('Jam pulang tidak boleh kosong');
            } else if ($entrytime > $hometime) {
                throw new Exception('Jam masuk tidak boleh lebih besar dari jam pulang');
            } else if ($entrytime == $hometime) {
                throw new Exception('Jam masuk tidak boleh sama dengan jam pulang');
            }

            $entry_late = DateTime::createFromFormat('H:i', $entrytime);

            if ($entry_late == false) {
                throw new Exception('Format jam masuk tidak valid');
            }

            $entry_late->modify('+' . $latetolerance_time . ' minutes');

            if ($latetolerance_time > 0 && $entry_late->format('H:i') >= $hometime) {
                throw new Exception('Toleransi terlambat tidak boleh lebih besar dari jam pulang');
            }

            $cekdays = $this->msattendancetime->get(array(
                'days' => $days,
                'id !=' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($cekdays->num_rows() > 0) {
                throw new Exception('Waktu Absensi hari ' . $days . ' sudah ada');
            }

            $update = array();
            $update['days'] = $days;
            $update['entry_time'] = $entrytime;
            $update['home_time'] = $hometime;
            $update['late_tolerance_time'] = $latetolerance_time;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msattendancetime->update(array('id' => $id), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal diubah');
            }

            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_delete_attendance()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $id = getPost('id');

            $get = $this->msattendancetime->get(array(
                'id' => $id
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $this->msattendancetime->delete(array('id' => $id));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal dihapus');
            } else {
                $this->db->trans_commit();

                return JSONResponseDefault('OK', 'Data berhasil dihapus');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function school()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Pengaturan Sekolah';
        $data['content'] = 'admin/settings/school/index';
        $data['settings'] = $this->msusers->get(array('id' => getCurrentIdUser()))->row();
        $data['province'] = $this->msprovince->get()->result();
        $data['city'] = $this->mscity->get(array('provinceid' => $data['settings']->provinceid ?? null))->result();
        $data['district'] = $this->msdistrict->get(array('cityid' => $data['settings']->cityid ?? null))->result();
        $data['subdistrict'] = $this->mssubdistrict->get(array('districtid' => $data['settings']->districtid ?? null))->result();

        return $this->load->view('master', $data);
    }

    public function process_school()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $cek = $this->msusers->get(array(
                'id' => getCurrentIdUser()
            ));

            if ($cek->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $cek->row();

            $schoolname = getPost('schoolname');
            $npsn = getPost('npsn');
            $provinceid = getPost('provinceid');
            $cityid = getPost('cityid');
            $districtid = getPost('districtid');
            $subdistrictid = getPost('subdistrictid');
            $phonenumber = getPost('phonenumber');
            $schoolemail = getPost('schoolemail');
            $address = getPost('address');
            $logo = isset($_FILES['logo']) ? $_FILES['logo'] : null;
            $logo_institution = isset($_FILES['logo_institution']) ? $_FILES['logo_institution'] : null;

            if ($row->logo == null && $logo != null && $logo['size'] == 0) {
                throw new Exception('Logo tidak boleh kosong');
            } else if ($schoolname == null) {
                throw new Exception('Nama sekolah tidak boleh kosong');
            } else if ($npsn == null) {
                throw new Exception('NPSN tidak boleh kosong');
            } else if ($provinceid == null) {
                throw new Exception('Provinsi tidak boleh kosong');
            } else if ($cityid == null) {
                throw new Exception('Kabupaten/Kota tidak boleh kosong');
            } else if ($districtid == null) {
                throw new Exception('Kecamatan tidak boleh kosong');
            } else if ($subdistrictid == null) {
                throw new Exception('Desa tidak boleh kosong');
            } else if ($phonenumber == null) {
                throw new Exception('Nomor telepon tidak boleh kosong');
            } else if ($schoolemail == null) {
                throw new Exception('Email sekolah tidak boleh kosong');
            } else if (!filter_var($schoolemail, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Email sekolah tidak valid');
            } else if ($address == null) {
                throw new Exception('Alamat tidak boleh kosong');
            }

            $update = array();

            if ($logo['size'] > 0) {
                $config = array();
                $config['allowed_types'] = 'jpg|jpeg|png';
                $config['upload_path'] = './uploads/logo';
                $config['encrypt_name'] = true;

                $this->load->library('upload', $config);

                if ($this->upload->do_upload('logo')) {
                    $data = $this->upload->data();
                    $update['logo'] = $data['file_name'];

                    if (file_exists('./uploads/logo/' . $row->logo)) {
                        @unlink('./uploads/logo/' . $row->logo);
                    }
                } else {
                    throw new Exception('Gagal mengunggah logo');
                }
            }

            if ($logo_institution['size'] > 0) {
                $config = array();
                $config['allowed_types'] = 'jpg|jpeg|png';
                $config['upload_path'] = './uploads/logo';
                $config['encrypt_name'] = true;

                $this->load->library('upload', $config);

                if ($this->upload->do_upload('logo_institution')) {
                    $data = $this->upload->data();
                    $update['logo_institution'] = $data['file_name'];

                    if (file_exists('./uploads/logo/' . $row->logo_institution)) {
                        @unlink('./uploads/logo/' . $row->logo_institution);
                    }
                } else {
                    throw new Exception('Gagal mengunggah logo lembaga pendidikan');
                }
            }

            $update['name'] = strtoupper($schoolname);
            $update['npsn'] = $npsn;
            $update['provinceid'] = $provinceid;
            $update['cityid'] = $cityid;
            $update['districtid'] = $districtid;
            $update['subdistrictid'] = $subdistrictid;
            $update['address'] = $address;
            $update['phonenumber'] = $phonenumber;
            $update['schoolemail'] = $schoolemail;
            $update['updatedby'] = getCurrentIdUser();
            $update['updateddate'] = getCurrentDate();

            $this->msusers->update(array('id' => getCurrentIdUser()), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal diubah');
            } else {
                $this->db->trans_commit();

                return JSONResponseDefault('OK', 'Data berhasil diubah');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function visionmission()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data['title'] = 'Profil Sekolah - Visi & Misi';
        $data['content'] = 'admin/schoolprofile/visionmission/index';

        $vision =  $this->tbsettings->get(array(
            'settings_name' => 'vision',
            'createdby' => getCurrentIdUser()
        ))->row()->settings_value ?? null;

        $mission =  $this->tbsettings->get(array(
            'settings_name' => 'mission',
            'createdby' => getCurrentIdUser()
        ))->row()->settings_value ?? null;

        $data['vision'] = $vision;
        $data['mission'] = $mission;

        return $this->load->view('master', $data);
    }

    public function process_visionmission()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $vision = getPost('vision');
            $mission = getPost('mission');

            if (empty($vision)) {
                throw new Exception('Visi tidak boleh kosong!');
            }

            if (empty($mission)) {
                throw new Exception('Misi tidak boleh kosong!');
            }

            $cekvision =  $this->tbsettings->get(array(
                'settings_name' => 'vision',
                'createdby' => getCurrentIdUser()
            ))->num_rows();

            if ($cekvision > 0) {
                $this->tbsettings->update(array(
                    'settings_name' => 'vision',
                    'createdby' => getCurrentIdUser()
                ), array('settings_value' => $vision));
            } else {
                $this->tbsettings->insert(array(
                    'settings_name' => 'vision',
                    'settings_value' => $vision,
                    'createdby' => getCurrentIdUser()
                ));
            }

            $cekmission =  $this->tbsettings->get(array(
                'settings_name' => 'mission',
                'createdby' => getCurrentIdUser()
            ))->num_rows();

            if ($cekmission > 0) {
                $this->tbsettings->update(array(
                    'settings_name' => 'mission',
                    'createdby' => getCurrentIdUser()
                ), array('settings_value' => $mission));
            } else {
                $this->tbsettings->insert(array(
                    'settings_name' => 'mission',
                    'settings_value' => $mission,
                    'createdby' => getCurrentIdUser()
                ));
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data Visi & Misi gagal diubah');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data Visi & Misi berhasil diubah');
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }
}
