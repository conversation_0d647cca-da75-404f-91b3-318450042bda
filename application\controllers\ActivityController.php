<?php
defined('BASEPATH') or die('No direct script access allowed!');

class ActivityController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsActivity', 'msactivity');
        $this->load->model('MsTypeactivity', 'mstypeactivity');
    }
    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Agenda - Agenda Kegiatan';
        $data['content'] = 'admin/master/activity/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $datatable = $this->datatables->make('MsActivity', 'QueryDatatables', 'SearchDatatables');

        $where = array();

        $where['a.createdby'] = getCurrentIdUser();

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = [];
            $detail[] = $value->title;
            $detail[] = $value->category_name;
            $detail[] = $value->description;
            $detail[] = tgl_indo($value->startdate);
            $detail[] = tgl_indo($value->enddate);
            $detail[] = $value->starttime;
            $detail[] = $value->endtime;
            $detail[] = $value->place;

            $actions = "<div class=\"d-flex\">
            <a href=\"" . base_url('master/activity/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" title=\"Ubah\">
                <i class=\"ti ti-edit\"></i>
            </a>
            <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"deleteActivity('" . $value->id . "')\" title=\"Hapus\">
                <i class=\"ti ti-trash\"></i>
            </button>
        
            <!-- Tombol untuk mengunduh file -->
            <a href=\"" . base_url('uploads/activity/' . $value->picture) . "\" class=\"btn btn-success btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" download title=\"Download\">
                <i class=\"ti ti-download\"></i>
            </a>
        </div>";


            $detail[] = $actions;
            $data[] = $detail;
        }
        return $datatable->json($data);
    }


    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['type'] = $this->mstypeactivity->get(array('createdby' => getCurrentIdUser()), null, null, 'name ASC')->result();
        $data['title'] = 'Agenda - Tambah Agenda Kegiatan';
        $data['content'] = 'admin/master/activity/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $this->db->trans_begin();

        $title =  getPost('title', '');
        $type = getPost('typeactivity');
        $description = getPost('description', '');
        $startdate = getPost('startdate');
        $starttime = getPost('starttime');
        $enddate = getPost('enddate');
        $endtime = getPost('endtime');
        $place = getPost('place', '');
        $picture = $_FILES['picture'];

        if (empty(trim($title))) {
            return JSONResponseDefault('FAILED', 'Judul harus diisi');
        }

        if (empty($type)) {
            return JSONResponseDefault('FAILED', 'Jenis Kegiatan harus dipilih');
        }

        if (empty(trim($description))) {
            return JSONResponseDefault('FAILED', 'Deskripsi Kegiatan harus diisi');
        }

        if (empty($startdate) || !strtotime($startdate)) {
            return JSONResponseDefault('FAILED', 'Tanggal mulai tidak valid');
        }

        if (empty($starttime)) {
            return JSONResponseDefault('FAILED', 'Waktu mulai harus diisi');
        }

        if (empty($enddate) || !strtotime($enddate)) {
            return JSONResponseDefault('FAILED', 'Tanggal berakhir tidak valid');
        }

        if (empty($endtime)) {
            return JSONResponseDefault('FAILED', 'Waktu berakhir harus diisi');
        }

        if (empty(trim($place))) {
            return JSONResponseDefault('FAILED', 'Tempat harus diisi');
        }

        if ($picture['size'] <= 0) {
            return JSONResponseDefault('FAILED', 'File harus diisi');
        }


        // Validasi tanggal mulai dan berakhir
        if (strtotime($startdate) > strtotime($enddate)) {
            return JSONResponseDefault('FAILED', 'Tanggal mulai tidak bisa lebih besar dari tanggal berakhir');
        }

        // Validasi waktu berakhir hanya jika tanggal mulai dan tanggal berakhir sama
        if ($startdate === $enddate && strtotime($starttime) > strtotime($endtime)) {
            return JSONResponseDefault('FAILED', 'Waktu mulai tidak bisa lebih besar dari waktu berakhir ');
        }

        // Proses upload file
        if (!empty($_FILES['picture']['name'])) {
            // Tentukan folder upload
            $config['upload_path'] = './uploads/activity/';
            $config['allowed_types'] = '*'; // Mengizinkan semua jenis file
            $config['file_name'] = uniqid(); // Nama file unik
            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('picture')) {
                return JSONResponseDefault('FAILED', $this->upload->display_errors());
            } else {
                // Mendapatkan data file yang diupload
                $upload_data = $this->upload->data();
                $picture = $upload_data['file_name']; // Nama file yang diupload
            }
        } else {
            $picture = null; // Tidak ada file yang diupload
        }

        // Simpan data ke database
        $insert = array();
        $insert['title'] = $title;
        $insert['type'] = $type;
        $insert['description'] = $description;
        $insert['startdate'] = $startdate;
        $insert['starttime'] = $starttime;
        $insert['enddate'] = $enddate;
        $insert['endtime'] = $endtime;
        $insert['place'] = $place;
        $insert['picture'] = $picture;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->msactivity->insert($insert);

        // Insert data agenda
        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Data gagal ditambahkan');
        } else {
            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Agenda kegiatan berhasil ditambahkan');
        }
    }
    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            redirect(base_url('auth/login/admin'));
        }

        $activity = $this->msactivity->get(array(
            'id' => $id
        ));

        if ($activity->num_rows() == 0) {
            return redirect(base_url('master/activity'));
        }

        $data = array();
        $data['agenda'] = $activity->row();
        $data['type'] = $this->mstypeactivity->get(array('createdby' => getCurrentIdUser()), null, null, 'name ASC')->result();
        $data['title'] = 'Agenda - Ubah Agenda Kegiatan';
        $data['content'] = 'admin/master/activity/edit';

        $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $this->db->trans_begin();

        // Ambil data dari POST request
        $title =  getPost('title', '');
        $type = getPost('typeactivity');
        $description = getPost('description', '');
        $startdate = getPost('startdate');
        $starttime = getPost('starttime');
        $enddate = getPost('enddate');
        $endtime = getPost('endtime');
        $place = getPost('place', '');
        $picture = $_FILES['picture'];

        // Validasi input
        if (empty(trim($title))) {
            return JSONResponseDefault('FAILED', 'Judul harus diisi');
        }
        if (empty($type)) {
            return JSONResponseDefault('FAILED', 'Jenis kegiatan harus dipilih');
        }
        if (empty(trim($description))) {
            return JSONResponseDefault('FAILED', 'Deskripsi Kegiatan harus diisi');
        }
        if (empty($startdate) || !strtotime($startdate)) {
            return JSONResponseDefault('FAILED', 'Tanggal mulai tidak valid');
        }
        if (empty($starttime)) {
            return JSONResponseDefault('FAILED', 'Waktu mulai harus diisi');
        }
        if (empty($enddate) || !strtotime($enddate)) {
            return JSONResponseDefault('FAILED', 'Tanggal berakhir tidak valid');
        }
        if (empty($endtime)) {
            return JSONResponseDefault('FAILED', 'Waktu berakhir harus diisi');
        }
        if (empty(trim($place))) {
            return JSONResponseDefault('FAILED', 'Tempat harus diisi');
        }


        // Validasi tanggal mulai dan berakhir
        if (strtotime($startdate) > strtotime($enddate)) {
            return JSONResponseDefault('FAILED', 'Tanggal mulai tidak bisa lebih besar dari tanggal berakhir');
        }

        // Validasi waktu berakhir hanya jika tanggal mulai dan tanggal berakhir sama
        if ($startdate === $enddate && strtotime($starttime) > strtotime($endtime)) {
            return JSONResponseDefault('FAILED', 'Waktu mulai tidak bisa lebih besar dari waktu berakhir');
        }

        // Ambil data agenda yang ada di database
        $agenda = $this->msactivity->get(array(
            'id' => $id
        ));

        if ($agenda->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Agenda tidak ditemukan');
        }

        $agenda = $agenda->row();

        // Proses data untuk update
        $update = array();
        $update['title'] = $title;
        $update['type'] = $type;
        $update['description'] = $description;
        $update['startdate'] = $startdate;
        $update['starttime'] = $starttime;
        $update['enddate'] = $enddate;
        $update['endtime'] = $endtime;
        $update['place'] = $place;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        // Jika ada file gambar yang di-upload
        if (!empty($picture['name'])) {
            // Tentukan folder upload
            $config['upload_path'] = './uploads/activity/';
            $config['allowed_types'] = '*'; // Mengizinkan semua jenis file
            $config['file_name'] = uniqid(); // Nama file unik
            $this->load->library('upload', $config);

            // Cek apakah upload berhasil
            if (!$this->upload->do_upload('picture')) {
                return JSONResponseDefault('FAILED', $this->upload->display_errors());
            } else {
                // Dapatkan nama file yang di-upload
                $upload_data = $this->upload->data();
                $update['picture'] = $upload_data['file_name']; // Simpan nama file

                // Hapus file gambar lama jika ada
                if (!empty($agenda->picture) && file_exists('./uploads/activity/' . $agenda->picture)) {
                    unlink('./uploads/activity/' . $agenda->picture);
                }
            }
        }

        // Lakukan update ke database
        $this->msactivity->update(array('id' => $id), $update);

        // Jika update berhasil
        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Data gagal diubah');
        } else {
            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Agenda kegiatan berhasil diubah');
        }
    }

    public function delete()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        if (empty($id)) {
            return JSONResponseDefault('FAILED', 'ID kegiatan tidak ditemukan');
        }

        // Ambil data kegiatan untuk mendapatkan nama file gambar
        $activity = $this->msactivity->get(array('id' => $id));

        if ($activity->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data kegiatan tidak ditemukan');
        }

        $activityData = $activity->row();

        // Hapus file gambar jika ada
        if (!empty($activityData->picture) && file_exists('./uploads/activity/' . $activityData->picture)) {
            unlink('./uploads/activity/' . $activityData->picture);
        }

        // Hapus data dari tabel
        $delete = $this->msactivity->delete(array('id' => $id));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Data gagal dihapus');
        }
    }
}
