<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsAcademicyear $msacademicyear
 * @property MsAcademicyeardetail $msacademicyeardetail
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 * @property Datatables $datatables
 * @property CI_Session $session
 */
class AcademicyearController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsAcademicyear', 'msacademicyear');
        $this->load->model('MsAcademicyeardetail', 'msacademicyeardetail');
    }

    public function index()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Tahun Ajaran';
        $data['content'] = 'admin/master/academicyear/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('MsAcademicyear', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.createdby'] = getCurrentIdUser();

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $semester = $value->semester == '1' ? 'Ganjil' : 'Genap';

            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('master/academicyear/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"deleteAcademicyear('" . $value->id . "','" . $value->startyear . "/" . $value->endyear . " Semester " . $semester . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                    <i class=\"ti ti-trash\"></i>
                </button>
            </div>";

            $checked = $value->isactive == '1' ? 'checked' : '';

            $status = "<label class=\"switch switch-primary\">
                <input type=\"checkbox\" class=\"switch-input\" onchange=\"changeIsactive('" . $value->id . "','" . $value->startyear . "/" . $value->endyear . " Semester " . $semester . "','" . $value->isactive . "')\" " . $checked . "/>

                <span class=\"switch-toggle-slider\">
                    <span class=\"switch-on\">
                        <i class=\"ti ti-check\"></i>
                    </span>

                    <span class=\"switch-off\">
                        <i class=\"ti ti-x\"></i>
                    </span>
                </span>

                <span class=\"switch-label\">Aktif</span>
            </label>";

            $detail[] = $value->startyear . '/' . $value->endyear;
            $detail[] = tgl_indo($value->startdateodd) . ' - ' . tgl_indo($value->enddateodd);
            $detail[] = tgl_indo($value->startdateeven) . ' - ' . tgl_indo($value->enddateeven);
            $detail[] = $status;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Tambah Tahun Ajaran';
        $data['content'] = 'admin/master/academicyear/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $startyear = getPost('startyear');
            $dateodd = getPost('dateodd');
            $dateeven = getPost('dateeven');

            if (empty(strlen(trim($startyear)))) {
                throw new Exception('Tahun ajaran tidak boleh kosong');
            } elseif ($dateodd == null) {
                throw new Exception('Semester ganjil tidak boleh kosong');
            } else if ($dateeven == null) {
                throw new Exception('Semester genap tidak boleh kosong');
            }

            $dateodd = explode('-', $dateodd);
            $startodd = DateFormat($dateodd[0], 'Y-m-d');
            $endodd = DateFormat($dateodd[1], 'Y-m-d');

            $dateeven = explode('-', $dateeven);
            $starteven = DateFormat($dateeven[0], 'Y-m-d');
            $endeven = DateFormat($dateeven[1], 'Y-m-d');

            $cek = $this->msacademicyear->select('id')->where(array('startyear' => $startyear, 'createdby' => getCurrentIdUser()))->get();

            if ($cek->num_rows() > 0) {
                throw new Exception('Tahun ajaran sudah ada');
            }

            if ($startodd > $starteven || $endodd > $starteven) {
                throw new Exception('Tanggal semester ganjil tidak boleh lebih besar dari semester genap');
            }

            $cekdate = $this->msacademicyeardetail->select('a.*')
                ->join('msacademicyear b', 'b.id = a.academicyearid')
                ->get(array(
                    'b.createdby' => getCurrentIdUser()
                ))->result();

            foreach ($cekdate as $key => $value) {
                foreach ($cekdate as $key => $value) {
                    $valueStart = date('Y-m-d', strtotime($value->startdate));
                    $valueEnd = date('Y-m-d', strtotime($value->enddate));

                    // Pengecekan untuk semester Ganjil
                    if (($startodd >= $valueStart && $startodd <= $valueEnd) ||
                        ($endodd >= $valueStart && $endodd <= $valueEnd) ||
                        ($startodd <= $valueStart && $endodd >= $valueStart) ||
                        ($startodd <= $valueEnd && $endodd >= $valueEnd) ||
                        ($startodd >= $valueStart && $endodd <= $valueEnd) ||
                        ($startodd <= $valueStart && $endodd >= $valueEnd)
                    ) {
                        throw new Exception('Tanggal semester ganjil sudah ada');
                    }

                    // Pengecekan untuk semester Genap
                    if (($starteven >= $valueStart && $starteven <= $valueEnd) ||
                        ($endeven >= $valueStart && $endeven <= $valueEnd) ||
                        ($starteven <= $valueStart && $endeven >= $valueStart) ||
                        ($starteven <= $valueEnd && $endeven >= $valueEnd) ||
                        ($starteven >= $valueStart && $endeven <= $valueEnd) ||
                        ($starteven <= $valueStart && $endeven >= $valueEnd)
                    ) {
                        throw new Exception('Tanggal semester genap sudah ada');
                    }
                }
            }

            $endyear = $startyear + 1;

            $insert = array();
            $insert['startyear'] = $startyear;
            $insert['endyear'] = $endyear;
            $insert['isactive'] = '0';
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->msacademicyear->insert($insert);
            $lastid = $this->db->insert_id();

            for ($i = 0; $i < 2; $i++) {
                $insertdetail = array();
                $insertdetail['academicyearid'] = $lastid;

                if ($i == 0) {
                    $insertdetail['startdate'] = $startodd;
                    $insertdetail['enddate'] = $endodd;
                    $insertdetail['semester'] = 'Ganjil';
                } else {
                    $insertdetail['startdate'] = $starteven;
                    $insertdetail['enddate'] = $endeven;
                    $insertdetail['semester'] = 'Genap';
                }

                $insertdetail['createddate'] = getCurrentDate();
                $insertdetail['createdby'] = getCurrentIdUser();

                $this->msacademicyeardetail->insert($insertdetail);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal ditambahkan');
            }

            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $cek = $this->msacademicyear->select('a.*, (SELECT DATE(startdate) FROM msacademicyeardetail WHERE academicyearid = a.id AND semester = "Ganjil" LIMIT 1) as startdateodd, (SELECT DATE(enddate) FROM msacademicyeardetail WHERE academicyearid = a.id AND semester = "Ganjil" LIMIT 1) as enddateodd, (SELECT DATE(startdate) FROM msacademicyeardetail WHERE academicyearid = a.id AND semester = "Genap" LIMIT 1) as startdateeven, (SELECT DATE(enddate) FROM msacademicyeardetail WHERE academicyearid = a.id AND semester = "Genap" LIMIT 1) as enddateeven')
            ->where('id', $id)
            ->where('createdby', getCurrentIdUser())
            ->get();

        if ($cek->num_rows() == 0) {
            return redirect(base_url('master/academicyear'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Ubah Tahun Ajaran';
        $data['content'] = 'admin/master/academicyear/edit';
        $data['academicyear'] = $cek->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $cek = $this->msacademicyear->get(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($cek->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $startyear = getPost('startyear');
            $dateodd = getPost('dateodd');
            $dateeven = getPost('dateeven');

            if (empty(strlen(trim($startyear)))) {
                throw new Exception('Tahun ajaran tidak boleh kosong');
            } elseif ($dateodd == null) {
                throw new Exception('Semester ganjil tidak boleh kosong');
            } else if ($dateeven == null) {
                throw new Exception('Semester genap tidak boleh kosong');
            }

            $dateodd = explode('-', $dateodd);
            $startodd = DateFormat($dateodd[0], 'Y-m-d');
            $endodd = DateFormat($dateodd[1], 'Y-m-d');

            $dateeven = explode('-', $dateeven);
            $starteven = DateFormat($dateeven[0], 'Y-m-d');
            $endeven = DateFormat($dateeven[1], 'Y-m-d');

            if ($startodd > $starteven || $endodd > $starteven) {
                throw new Exception('Tanggal semester ganjil tidak boleh lebih besar dari semester genap');
            }

            $cekdate = $this->msacademicyeardetail->select('a.*')
                ->join('msacademicyear b', 'b.id = a.academicyearid')
                ->get(array(
                    'a.academicyearid !=' => $id,
                    'b.createdby' => getCurrentIdUser()
                ))->result();

            foreach ($cekdate as $key => $value) {
                $valueStart = date('Y-m-d', strtotime($value->startdate));
                $valueEnd = date('Y-m-d', strtotime($value->enddate));

                // Pengecekan untuk semester Ganjil
                if (($startodd >= $valueStart && $startodd <= $valueEnd) ||
                    ($endodd >= $valueStart && $endodd <= $valueEnd) ||
                    ($startodd <= $valueStart && $endodd >= $valueStart) ||
                    ($startodd <= $valueEnd && $endodd >= $valueEnd) ||
                    ($startodd >= $valueStart && $endodd <= $valueEnd) ||
                    ($startodd <= $valueStart && $endodd >= $valueEnd)
                ) {
                    throw new Exception('Tanggal semester ganjil sudah ada');
                }

                // Pengecekan untuk semester Genap
                if (($starteven >= $valueStart && $starteven <= $valueEnd) ||
                    ($endeven >= $valueStart && $endeven <= $valueEnd) ||
                    ($starteven <= $valueStart && $endeven >= $valueStart) ||
                    ($starteven <= $valueEnd && $endeven >= $valueEnd) ||
                    ($starteven >= $valueStart && $endeven <= $valueEnd) ||
                    ($starteven <= $valueStart && $endeven >= $valueEnd)
                ) {
                    throw new Exception('Tanggal semester genap sudah ada');
                }
            }

            $update = array();
            $update['startyear'] = $startyear;
            $update['endyear'] = $startyear + 1;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msacademicyear->update(array('id' => $id), $update);

            $updateodd = array();
            $updateodd['startdate'] = $startodd;
            $updateodd['enddate'] = $endodd;
            $updateodd['updateddate'] = getCurrentDate();
            $updateodd['updatedby'] = getCurrentIdUser();

            $this->msacademicyeardetail->update(array('academicyearid' => $id, 'semester' => 'Ganjil'), $updateodd);

            $updateeven = array();
            $updateeven['startdate'] = $starteven;
            $updateeven['enddate'] = $endeven;
            $updateeven['updateddate'] = getCurrentDate();
            $updateeven['updatedby'] = getCurrentIdUser();

            $this->msacademicyeardetail->update(array('academicyearid' => $id, 'semester' => 'Genap'), $updateeven);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal diubah');
            }

            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_delete()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $id = getPost('id');

            $get = $this->msacademicyear->get(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $this->msacademicyear->delete(array('id' => $id));
            $this->msacademicyeardetail->delete(array('academicyearid' => $id));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal dihapus');
            }

            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_change_isactive()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $id = getPost('id');

            $get = $this->msacademicyear->get(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $update = array();
            $update['isactive'] = '0';
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msacademicyear->update(array('isactive' => '1', 'createdby' => getCurrentIdUser()), $update);

            $update2 = array();
            $update2['isactive'] = $row->isactive == '1' ? '0' : '1';
            $update2['updateddate'] = getCurrentDate();
            $update2['updatedby'] = getCurrentIdUser();

            $this->msacademicyear->update(array('id' => $id), $update2);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Tahun ajaran gagal diubah');
            }

            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Tahun ajaran berhasil diubah');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_change_all()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $id = getPost('id');

            $get = $this->msacademicyear->get(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Tahun ajaran gagal diubah');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Tahun ajaran berhasil diubah');
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }
}
