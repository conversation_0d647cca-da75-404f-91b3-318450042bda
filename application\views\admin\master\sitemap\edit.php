<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<h4 class="py-3 mb-4">
    <span class="text-muted fw-light">Manajemen / Peta Situs / </span> Ubah Peta Situs
</h4>

<div class="card">
    <h5 class="card-header">Formulir Ubah Peta Situs</h5>

    <form id="frmSitemap" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
        <div class="card-body">
            <div class="mb-3">
                <label for="name" class="form-label">Nama Peta Situs<span class="text-danger">*</span></label>
                <input type="text" placeholder="Masukkan Nama Peta Situs" id="name" name="name" class="form-control" value="<?= $data->name ?>" required>
            </div>

            <div class="mb-3">
                <label for="link" class="form-label">Link Situs<span class="text-danger">*</span></label>
                <input type="text" placeholder="Masukkan Link Situs" id="link" name="link" class="form-control" value="<?= $data->link ?>" required>
            </div>

            <div class="d-flex justify-content-end mt-4">
                <a href="<?= base_url('master/sitemap') ?>" class="btn btn-danger">
                    <i class="ti ti-arrow-back me-2"></i>
                    Kembali
                </a>

                <button type="submit" class="btn btn-primary ms-2">
                    <i class="ti ti-check me-2"></i>
                    Simpan
                </button>
            </div>
        </div>
    </form>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmSitemap', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, (ok) => {
                        return window.location.href = '<?= base_url('master/sitemap') ?>';
                    });
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });
    };
</script>