<?php


defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsAds $msads
 * @property CI_DB_query_builder $db
 * @property Datatables $datatables
 */
class AdsController extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsAds', 'msads');
    }


    public function index()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Profil Sekolah - Iklan';
        $data['content'] = 'admin/schoolprofile/ads/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('MsAds', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.createdby'] = getCurrentIdUser();

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $detail[] = '<img src="' . base_url('uploads/ads/' . $value->picture) . '" style="width: 100px; height: 80px; object-fit: cover; object-position: center; border-radius: 8px;" class="img-fluid">';
            $detail[] = '<a href="' . htmlspecialchars($value->link) . '" target="_blank">'
                . htmlspecialchars($value->link)
                . '</a>';
            $detail[] = tgl_indo($value->startdate);
            $detail[] = tgl_indo($value->enddate);
            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('master/ads/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                            <i class=\"ti ti-edit\"></i>
                        </a>
                <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"deleteAds('" . $value->id . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                            <i class=\"ti ti-trash\"></i>
                        </button>
                    </div>";

            $checked = $value->ispublish == '1' ? 'checked' : '';
            $statusText = $value->ispublish == '1' ? 'Publish' : 'Unpublish';
            $status = "<label class=\"switch switch-primary\">
                <input type=\"checkbox\" class=\"switch-input\" id=\"ispublish_" . $value->id . "\" onchange=\"changeIspublish('" . $value->id . "','" . $value->ispublish . "')\" " . $checked . "/>
                        <span class=\"switch-toggle-slider\">
                            <span class=\"switch-on\">
                                <i class=\"ti ti-check\"></i>
                            </span>

                            <span class=\"switch-off\">
                                <i class=\"ti ti-x\"></i>
                            </span>
                        </span>
                        <span class=\"switch-label\" id=\"status_text_" . $value->id . "\">" . $statusText . "</span>
                     </label>";

            // Add status and actions to detail
            $detail[] = $status;
            $detail[] = $actions;

            // Append detail to data
            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return  redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Profil Sekolah - Tambah Iklan';
        $data['content'] = 'admin/schoolprofile/ads/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $link = getPost('link');
            $startdate = getPost('startdate');
            $enddate = getPost('enddate');
            $picture = isset($_FILES['picture']['name']) ? $_FILES['picture'] : null;
            $ispublish = getPost('ispublish') ?? '0';

            // Validasi input
            if ($link == null) {
                throw new Exception('Link / URL tidak boleh kosong');
            } else if ($startdate == null) {
                throw new Exception('Tanggal mulai tidak boleh kosong');
            } else if ($enddate == null) {
                throw new Exception('Tanggal berakhir tidak boleh kosong');
            } else if ($startdate > $enddate) {
                throw new Exception('Tanggal mulai tidak boleh lebih besar dari tanggal berakhir');
            } else if (strtotime($startdate) >= strtotime($enddate)) {
                throw  new Exception('Tanggal Berakhir harus lebih besar dari tanggal rilis.');
            }

            $picturePath = null;
            if ($picture['size'] > 0) {
                $config['upload_path'] = './uploads/ads/';
                $config['allowed_types'] = 'jpg|jpeg|png|webp';
                $config['max_size'] = 3072;
                $config['encrypt_name'] = true;

                $this->load->library('upload', $config);

                if (!$this->upload->do_upload('picture')) {
                    throw new Exception($this->upload->display_errors());
                }

                $uploadData = $this->upload->data();
                $picturePath = $uploadData['file_name'];
            }
            $insert = [
                'link' => $link,
                'picture' => $picturePath,
                'startdate' => $startdate,
                'enddate' => $enddate,
                'ispublish' => $ispublish,
                'createddate' => getCurrentDate(),
                'createdby' => getCurrentIdUser()
            ];

            $this->msads->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal ditambahkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data Iklan berhasil ditambahkan');
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $ads = $this->msads->get(array('id' => $id));

        if ($ads->num_rows() == 0) {
            return redirect('master/ads');
        }

        $data = array();
        $data['title'] = 'Profil Sekolah - Ubah Iklan';
        $data['content'] = 'admin/schoolprofile/ads/edit';
        $data['ads'] = $ads->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        try {
            // Mulai transaksi database
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            // Ambil data iklan berdasarkan ID
            $ads = $this->msads->get(array('id' => $id));

            if ($ads->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            // Ambil inputan data
            $link = trim(getPost('link'));
            $picture = isset($_FILES['picture']['name']) ? $_FILES['picture'] : null;
            $startdate = getPost('startdate');
            $enddate = getPost('enddate');
            $ispublish = getPost('ispublish') ?? '0';

            if (empty($link)) {
                throw new Exception('Link/url Tidak Boleh Kosong');
            } else if (empty($startdate)) {
                throw new Exception('Tanggal rilis tidak boleh kosong');
            } else if (empty($enddate)) {
                throw new Exception('Tanggal berakhir tidak boleh kosong');
            } else if (strtotime($startdate) >= strtotime($enddate)) {
                throw new Exception('Tanggal berakhir harus lebih besar dari tanggal rilis');
            }

            // Persiapkan data untuk diperbarui
            $update = array(
                'link' => $link,
                'startdate' => $startdate,
                'enddate' => $enddate,
                'ispublish' => $ispublish,
                'updateddate' => getCurrentDate(),
                'updatedby' => getCurrentIdUser(),
            );

            // Proses gambar jika ada yang diunggah
            if (!empty($picture['name'])) {
                $config['upload_path'] = './uploads/ads/';
                $config['allowed_types'] = 'jpg|jpeg|png|webp';
                $config['max_size'] = 3048; // Maksimal ukuran file 3MB
                $config['encrypt_name'] = true;

                $this->load->library('upload', $config);

                if (!$this->upload->do_upload('picture')) {
                    throw new Exception($this->upload->display_errors());
                }

                $upload_data = $this->upload->data();
                $picturePath = $upload_data['file_name'];
                $update['picture'] = $picturePath;

                // Hapus gambar lama jika ada
                if (!empty($ads->row()->picture) && file_exists('./uploads/ads/' . $ads->row()->picture)) {
                    unlink('./uploads/ads/' . $ads->row()->picture);
                }
            }

            // Perbarui data iklan
            $this->msads->update(array('id' => $id), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data Iklan gagal diubah');
            } else {
                // Jika berhasil, commit transaksi
                $this->db->trans_commit();

                return JSONResponseDefault('OK', 'Data Iklan berhasil diubah');
            }
        } catch (Exception $e) {
            // Rollback transaksi jika terjadi kesalahan
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_delete()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda Tidak memiliki akses!');
        }

        $id = getPost('id');

        if (empty($id)) {
            return JSONResponseDefault('FAILED', 'Data Tidak ditemukan');
        }

        $ads = $this->msads->get(array('id' => $id, 'createdby' => getCurrentIdUser()));

        if ($ads->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data Iklan tidak ditemukan');
        }

        $old_picture = $ads->row()->picture;

        if (!empty($old_picture) && file_exists('./uploads/ads/' . $old_picture)) {
            if (!unlink('./uploads/ads/' . $old_picture)) {
                return JSONResponseDefault('FAILED', 'Gagal menghapus file gambar');
            }
        }

        // Hapus data dari database
        $delete = $this->msads->delete(array('id' => $id));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data Iklan berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Data Iklan gagal dihapus');
        }
    }

    public function process_change_ispublish()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $id = getPost('id');

            $get = $this->msads->get(array('id' => $id, 'createdby' => getCurrentIdUser()));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();
            $newStatus = $row->ispublish == '1' ? '0' : '1';

            $update = [
                'ispublish' => $newStatus,
                'updateddate' => getCurrentDate(),
                'updatedby' => getCurrentIdUser()
            ];

            $this->msads->update(array('id' => $id), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Terjadi kesalahan saat memperbarui status iklan.');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Status iklan berhasil diubah');
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }
}
