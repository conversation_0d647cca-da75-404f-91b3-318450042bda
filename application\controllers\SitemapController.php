<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsSitemap $mssitemap
 * @property Datatables $datatables
 */
class SitemapController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsSitemap', 'mssitemap');
    }

    public function index()
    {
        if (!isLogin() && !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Peta Situs';
        $data['content'] = 'admin/master/sitemap/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() && !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk melihat data peta situs');
        }

        $datatables = $this->datatables->make('MsSitemap', 'QueryDatatables', 'SearchDatatables');

        $where = array();

        if (isAdmin()) {
            $where['a.createdby'] = getCurrentIdUser();
        }

        $data = array();

        foreach ($datatables->getData($where) as $key => $value) {
            $detail = array();
            $detail[] = $value->name;
            $detail[] = "<a href=\"$value->link\" target=\"_blank\">$value->link</a>";
            $detail[] = "<div class=\"d-flex\">
                <a href=\"" . base_url('master/sitemap/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2 mb-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm ms-2 mb-2\" onclick=\"deleteSitemap('" . $value->id . "', '" . $value->name . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                    <i class=\"ti ti-trash\"></i>
                </button>
            </div>";

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function add()
    {
        if (!isLogin() && !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Tambah Peta Situs';
        $data['content'] = 'admin/master/sitemap/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin() && !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menambahkan peta situs');
        }

        $name = trim(getPost('name'));
        $link = trim(getPost('link'));

        if (empty($name)) {
            return JSONResponseDefault('FAILED', 'Nama peta situs tidak boleh kosong');
        } else if (empty($link)) {
            return JSONResponseDefault('FAILED', 'Link peta situs tidak boleh kosong');
        }

        $insert = array();
        $insert['name'] = $name;
        $insert['link'] = $link;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->mssitemap->insert($insert);

        return JSONResponseDefault('OK', 'Peta situs berhasil ditambahkan');
    }

    public function edit($id)
    {
        if (!isLogin() && !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $cek = $this->mssitemap->get(array('id' => $id, 'createdby' => getCurrentIdUser()));

        if ($cek->num_rows() == 0) {
            return redirect(base_url('master/sitemap'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Ubah Peta Situs';
        $data['content'] = 'admin/master/sitemap/edit';
        $data['data'] = $cek->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin() && !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk mengubah peta situs');
        }

        $cek = $this->mssitemap->get(array('id' => $id, 'createdby' => getCurrentIdUser()));

        if ($cek->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Peta situs tidak ditemukan');
        }

        $name = trim(getPost('name'));
        $link = trim(getPost('link'));

        if (empty($name)) {
            return JSONResponseDefault('FAILED', 'Nama peta situs tidak boleh kosong');
        } else if (empty($link)) {
            return JSONResponseDefault('FAILED', 'Link peta situs tidak boleh kosong');
        }

        $update = array();
        $update['name'] = $name;
        $update['link'] = $link;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->mssitemap->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Peta situs berhasil diubah');
    }

    public function process_delete()
    {
        if (!isLogin() && !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus peta situs');
        }

        $id = getPost('id');

        $cek = $this->mssitemap->get(array('id' => $id, 'createdby' => getCurrentIdUser()));

        if ($cek->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Peta situs tidak ditemukan');
        }

        $this->mssitemap->delete(array('id' => $id));

        return JSONResponseDefault('OK', 'Peta situs berhasil dihapus');
    }
}
