<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsPosition $msposition
 * @property CI_DB_query_builder $db
 * @property Datatables $datatables
 */
class PositionController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsPosition', 'msposition');
    }

    public function index()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Jabatan';
        $data['content'] = 'admin/master/teacher/position/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('MsPosition', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.createdby'] = getCurrentIdUser();

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('master/position/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"deletePosition('" . $value->id . "','" . $value->name . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                    <i class=\"ti ti-trash\"></i>
                </button>
            </div>";

            $detail[] = $value->name;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Tambah Jabatan';
        $data['content'] = 'admin/master/teacher/position/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $name = trim(getPost('name', ''));

        if ($name == null) {
            return JSONResponseDefault('FAILED', 'Nama jabatan tidak boleh kosong');
        }

        $name = strtoupper($name);

        $cekposition = $this->msposition->select('name')->where(
            array(
                'name' => $name,
                'createdby' => getCurrentIdUser()
            )
        )->get();

        if ($cekposition->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Nama jabatan sudah digunakan');
        }

        $insert = array();
        $insert['name'] = $name;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $insert = $this->msposition->insert($insert);

        if ($insert) {
            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Data gagal ditambahkan');
        }
    }

    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $cek = $this->msposition->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($cek->num_rows() == 0) {
            return redirect(base_url('master/position'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Ubah Jabatan';
        $data['content'] = 'admin/master/teacher/position/edit';
        $data['position'] = $cek->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $cek = $this->msposition->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($cek->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $cek->row();

        $name = trim(getPost('name', ''));

        if ($name == null) {
            return JSONResponseDefault('FAILED', 'Nama jabatan tidak boleh kosong');
        }

        $name = strtoupper($name);

        if ($name != strtoupper($row->name)) {
            $cekposition = $this->msposition->select('name')->where(
                array(
                    'name' => $name,
                    'createdby' => getCurrentIdUser(),
                    'id !=' => $id
                )
            )->get();

            if ($cekposition->num_rows() > 0) {
                return JSONResponseDefault('FAILED', 'Nama jabatan sudah digunakan');
            }
        }

        $update = array();
        $update['name'] = $name;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $update = $this->msposition->update(array('id' => $id), $update);

        if ($update) {
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Data gagal diubah');
        }
    }

    public function process_delete()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $id = getPost('id');

        $get = $this->msposition->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $delete = $this->msposition->delete(array('id' => $id));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Data gagal dihapus');
        }
    }
}
